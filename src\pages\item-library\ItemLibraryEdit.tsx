import { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import {
  Box,
  Card,
  FormControl,
  Grid2 as Grid,
  IconButton,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Select,
  Tooltip,
  Typography,
} from '@mui/material';
import { EditDialog } from '@react-admin/ra-form-layout';
import {
  AutocompleteInput,
  BooleanInput,
  NumberInput,
  required,
  SaveButton,
  SelectInput,
  SimpleForm,
  TextField,
  TextInput,
  TranslatableInputs,
  useGetList,
  useLocaleState,
  useRecordContext,
  useRedirect,
  useResourceContext,
} from 'react-admin';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { CustomDivider } from '~/components/atoms/CustomDivider';
import {
  MuiSwitchInput,
  SwitchInput,
} from '~/components/atoms/inputs/SwitchInput';
import ModalHeader from '~/components/molecules/ModalHeader';
import { PublicNameAndDescriptionSection } from '~/components/molecules/PublicNameAndDescriptionSection';
import { RaFileUploadComponent } from '~/components/organisms/FileUpload';
import NewFeatureTag from '~/components/organisms/menu-catalog-dnd/add-item-modal/NewFeatureTag';
import {
  useGetListHospitalityCatalogsLive,
  useGetListHospitalityCategoriesLive,
  useGetListMeasureUnits,
  useGetListVatsLive,
} from '~/providers/resources';
import { getCurrencySymbolByCode } from '~/providers/utils/getCurrencySymbol';
import getFullscreenModalProps from '~/utils/getFullscreenModalProps';
import {
  createStandardImageConfig,
  resolveImageEditorConfig,
} from '~/utils/standardImageEditor';
import AgeRestrictionModal from '../../components/organisms/menu-catalog-dnd/add-item-modal/AgeRestrictionModal';
import NutritionalValuesModal, {
  NutritionalValues,
} from '../../components/organisms/menu-catalog-dnd/add-item-modal/NutritionalValuesModal';
import { MenuItem as MenuItemI } from '../../components/organisms/menu-catalog-dnd/types';

interface EditItemModalProps {
  path?: string;
  initialValues?: MenuItemI;
}

const PriceVariationsSection = (props: any) => {
  const { t } = useTranslation('');
  const { catalogSpecific, getCatalogName, vatsChoices } = props;

  // TODO! something about the flicker ... this whole part doesn't render because temporarily there are no vats
  if (vatsChoices.length === 0) return null;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Typography variant="h5">{t('itemLibrary.variations')}</Typography>
      {Object.entries(catalogSpecific).map(
        ([catalogId, catalogSpecificValues]: [string, any]) => {
          const catalogName = getCatalogName(catalogId);
          if (!catalogName) return null;
          return (
            <Box key={catalogId}>
              <Box>
                <Grid container spacing={2} alignItems="center">
                  <Grid size={{ xs: 4 }}>
                    <TextField source="" defaultValue={catalogName} />
                  </Grid>
                  <Grid size={{ xs: 4 }}>
                    <NumberInput
                      defaultValue={catalogSpecificValues.price}
                      source={`catalogSpecific.${catalogId}.price`}
                      label={t('shared.price')}
                      variant="outlined"
                      format={value => value / 10000}
                      parse={value => Math.floor(value * 10000)}
                      validate={[required()]}
                      slotProps={{
                        input: {
                          endAdornment: getCurrencySymbolByCode('RON'),
                        },
                      }}
                    />
                  </Grid>
                  <Grid size={{ xs: 4 }}>
                    <SelectInput
                      source={`catalogSpecific.${catalogId}.vat`}
                      label={t('shared.tva')}
                      choices={vatsChoices}
                      translateChoice={false}
                      validate={required()}
                    />
                  </Grid>
                </Grid>
              </Box>
            </Box>
          );
        }
      )}
    </Box>
  );
};

const ItemLibraryEditInner = () => {
  const [locale] = useLocaleState();
  const redirect = useRedirect();
  const resource = useResourceContext();
  const record = useRecordContext();
  const { t } = useTranslation('');
  const { setValue, getValues, watch } = useFormContext();

  const [isOpenAgeModal, setIsOpenAgeModal] = useState(false);
  const [isOpenNutritionalValuesModal, setIsOpenNutritionalValuesModal] =
    useState(false);

  // Watch form values
  const catalogSpecific = watch('catalogSpecific') || {};
  const age = watch('age');
  const dietaryPreferences = watch('dietaryPreferences') || [];
  const allergens = watch('allergens') || [];
  const nutritionalValues = watch('nutritionalValues');
  const images = watch('images') || [];

  const { data: catalogs } = useGetListHospitalityCatalogsLive();
  const getCatalogName = useCallback(
    (catalogId: string) => {
      const catalog = catalogs?.find((c: any) => c.id === catalogId);
      if (catalog) {
        return catalog.name;
      } else {
        return undefined;
      }
    },
    [catalogs]
  );

  const { data: vats } = useGetListVatsLive();
  const vatsChoices = useMemo(() => {
    if (!vats) return [];
    return vats.map((vat: any) => ({
      id: vat.value,
      name: `${vat.value}%`,
    }));
  }, [vats]);

  const handleClose = () => {
    redirect('list', resource, record?.id, undefined, {
      _scrollToTop: false,
    });
  };

  const updateValue = (field: string, value: unknown) => {
    setValue(field, value);
  };

  const dietaryPreferencesOptions = [
    'dairy-free',
    'gluten-free',
    'halal',
    'kosher',
    'nut-free',
    'vegan',
    'vegetarian',
    'low-sugar',
    'low-carb',
    'low-sodium',
  ];

  const allergensOptions = [
    'celery',
    'crustaceans',
    'eggs',
    'fish',
    'gluten',
    'lupin',
    'milk',
    'molluscs',
    'mustard',
    'peanuts',
    'sesame',
    'soy',
    'sulphites',
    'tree-nuts',
  ];

  const saveAgeRestriction = (ageValue: number) => {
    updateValue('age', ageValue);
    setIsOpenAgeModal(false);
  };

  const saveNutritionalValues = (values: NutritionalValues) => {
    updateValue('nutritionalValues', values);
    setIsOpenNutritionalValuesModal(false);
  };

  const handleMultiSelectChange = (
    event: any,
    name: 'dietaryPreferences' | 'allergens'
  ) => {
    const {
      target: { value },
    } = event;
    updateValue(name, typeof value === 'string' ? value.split(',') : value);
  };

  const { data: categories } = useGetListHospitalityCategoriesLive({
    filter: { _d: false },
  });

  const { data: measureUnits } = useGetListMeasureUnits();
  const MeasureUnitsOptionRenderer = () => {
    const record = useRecordContext();
    return (
      <Box
        sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}
      >
        <span>
          {record!.name[locale]}{' '}
          {record!.symbol[locale] ? `(${record!.symbol[locale]})` : ''}
        </span>
        <span>{t(`measureUnits.types.${record!.type}`)}</span>
      </Box>
    );
  };

  const measureUnitsOptionText = <MeasureUnitsOptionRenderer />;

  interface MeasureUnitChoice {
    name: Record<string, string>;
    symbol: Record<string, string>;
    type: string;
  }

  const measureUnitsInputText = (choice: MeasureUnitChoice): string =>
    `${choice.name[locale]}${choice.symbol[locale] ? ` (${choice.symbol[locale]})` : ''}`;

  const measureUnitsMatchSuggestion = (
    filter: string,
    choice: MeasureUnitChoice
  ): boolean => {
    return `${choice.name[locale]}${choice.symbol[locale] ? ` (${choice.symbol[locale]})` : ''}`
      .toLocaleLowerCase()
      .includes(filter.toLowerCase());
  };

  console.log(record);
  return (
    <>
      <ModalHeader handleClose={handleClose} title={t('itemsLibrary.editItem')}>
        <SaveButton type="submit" label={t('shared.save')} icon={<></>} />
      </ModalHeader>
      <Box
        sx={{
          padding: 3,
          width: '100%',
          maxWidth: '800px',
          margin: 'auto',
          display: 'flex',
          flexDirection: 'column',
          gap: 3,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column-reverse', md: 'row' },
            gap: 2,
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Box sx={{ width: '100%' }}>
            <Box>
              <Typography variant="h5">{t('menu.details')}</Typography>
            </Box>
            <Box>
              <TextInput source="name" label={t('shared.name')} required />
            </Box>
            <Box>
              <AutocompleteInput
                defaultValue=""
                source="groupId"
                label={t('shared.category_capitalize')}
                choices={categories}
                isRequired
              />
            </Box>
            <Box>
              <AutocompleteInput
                defaultValue=""
                source="measureUnit"
                label={t('measure-units.title')}
                choices={measureUnits}
                optionText={measureUnitsOptionText}
                inputText={measureUnitsInputText}
                matchSuggestion={measureUnitsMatchSuggestion}
                isRequired
              />
            </Box>
          </Box>
          <img
            style={{
              height: '220px',
              borderRadius: '8px',
              objectFit: 'cover',
            }}
            src={images && images.length > 0 ? images[0]?.url : '/assets/placeholder-item.svg'}
            alt="placeholder"
          />
        </Box>

        <CustomDivider />
        <PriceVariationsSection
          catalogSpecific={catalogSpecific}
          getCatalogName={getCatalogName}
          vatsChoices={vatsChoices}
        />
        <CustomDivider />
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="h5">
            {t('itemLibrary.identifications')}
          </Typography>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: 2,
              mb: 2,
            }}
          >
            <TextInput source="sku" label="SKU" />
            <TextInput source="gtin" label="GTIN" />
            <TextInput source="ean" label="EAN" />
          </Box>
        </Box>
        <CustomDivider />
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="h5">{t('menu.media')}</Typography>
          <RaFileUploadComponent
            source="images"
            label={t('menu.media')}
            config={{
              fileType: 'public',
              multiple: true,
              maxFiles: 3,
              maxSize: 50 * 1024 * 1024, // 50MB to accommodate videos
              acceptedTypes: [
                'image/jpeg',
                'image/png',
                'image/webp',
                'video/mp4',
                'video/webm',
                'video/quicktime',
              ],
              imageConfig: (() => {
                const resolved = resolveImageEditorConfig(
                  createStandardImageConfig.itemsImages()
                );
                return {
                  targetSizes: resolved.targetSizes,
                  autoGenerateThumbnail: true,
                  quality: 0.9,
                  editorConfig: resolved, // Pass the full resolved config as editorConfig
                };
              })(),
              ui: {
                disabled: false,
                readOnly: false,
              },
              callbacks: {
                onImageEdit: (original: File, edited: File[]) => {
                  console.log('Menu item media edited:', {
                    original: original.name,
                    editedCount: edited.length,
                    editedSizes: edited.map((f: File) => ({
                      name: f.name,
                      size: f.size,
                    })),
                  });
                },
              },
            }}
          />
        </Box>
        <CustomDivider />

        <Box>
          <Typography variant="h5">{t('menu.ordering')}</Typography>
          <Box
            sx={{
              display: 'flex',
              alignItems: {
                xs: 'flex-start',
                md: 'center',
              },
              justifyContent: 'space-between',
              mt: 3,
            }}
          >
            <Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: { xs: 1, md: 4 },
                }}
              >
                <Typography fontWeight={500}>
                  {t('menu.excludedFromDiscount')}
                </Typography>
                <NewFeatureTag />
              </Box>
              <Typography
                variant="body2"
                fontWeight={100}
                mt={0.5}
                maxWidth={600}
              >
                {t('menu.excludedFromDiscountTooltip')}
              </Typography>
            </Box>
            <SwitchInput
              source="excludedFromDiscount"
              label={t('menu.excludedFromDiscount')}
            />
          </Box>
        </Box>

        <Box>
          <CustomDivider />
        </Box>

        {/* Restrictions Section */}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="h5">Restrictions</Typography>

          {/* Age Restriction */}
          <Box
            onClick={() => setIsOpenAgeModal(true)}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              border: 'solid 1px',
              borderColor: 'custom.gray400',
              borderRadius: 2,
              height: '56px',
              paddingX: 2,
              ':hover': {
                cursor: 'pointer',
                borderColor: 'custom.gray800',
              },
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <Typography color="textSecondary" fontSize="14px">
                {t('menu.ageRestriction')}
              </Typography>
              <Tooltip title={t('menu.ageRestrictionTooltip')}>
                <IconButton>
                  <InfoOutlinedIcon color="disabled" />
                </IconButton>
              </Tooltip>
            </Box>
            {age ? (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <Box>
                  <Typography
                    color="textSecondary"
                    fontSize="15px"
                    textAlign="left"
                    fontWeight={'bold'}
                  >
                    {age === 18
                      ? t('menu.ageRestriction18')
                      : `${t('menu.ageRestriction')} ${age}`}
                  </Typography>
                </Box>
              </Box>
            ) : (
              <Typography color="primary" fontSize="14px">
                {t('shared.set')}
              </Typography>
            )}
            {age ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <img src="/assets/icons/check.svg" alt="check-icon" />
              </Box>
            ) : (
              ''
            )}
          </Box>
        </Box>

        <CustomDivider />

        <PublicNameAndDescriptionSection
          catalogSpecific={catalogSpecific}
          getCatalogName={getCatalogName}
        />

        <Box>
          <CustomDivider />
        </Box>

        <Box>
          <Typography variant="h5">
            {t('menu.nutritionalInformation')}
          </Typography>
        </Box>

        <Box>
          <NumberInput
            source="calories"
            fullWidth
            label={t('menu.calorieCount')}
            sx={{ mb: 3 }}
          />

          {/* Dietary Preferences */}
          <FormControl fullWidth>
            <InputLabel id="dietary-preferences-label">
              {t('menu.dietaryPreferences')}
            </InputLabel>
            <Select
              fullWidth
              labelId="dietary-preferences-label"
              id="dietary-preferences-checkbox"
              multiple
              value={dietaryPreferences}
              onChange={event =>
                handleMultiSelectChange(event, 'dietaryPreferences')
              }
              input={<OutlinedInput label="Dietary preferences" />}
              renderValue={selected =>
                selected
                  .map((el: string) =>
                    t(`add-item.dietary-preferences.${el}.title`)
                  )
                  .join(', ')
              }
              sx={{ mb: 3 }}
            >
              {dietaryPreferencesOptions.map(key => (
                <MenuItem key={key} value={key}>
                  <ListItemText
                    primary={t(`add-item.dietary-preferences.${key}.title`)}
                    secondary={t(
                      `add-item.dietary-preferences.${key}.description`
                    )}
                  />
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Allergens */}
          <FormControl fullWidth>
            <InputLabel id="allergens-label">{t('menu.allergens')}</InputLabel>
            <Select
              fullWidth
              labelId="allergens-label"
              id="allergens-checkbox"
              multiple
              value={allergens}
              onChange={event => handleMultiSelectChange(event, 'allergens')}
              input={<OutlinedInput label={t('menu.allergens')} />}
              renderValue={selected =>
                selected
                  .map((el: string) => t(`add-item.allergens.${el}.title`))
                  .join(', ')
              }
              sx={{ mb: 3 }}
            >
              {allergensOptions.map(key => (
                <MenuItem key={key} value={key}>
                  <ListItemText
                    primary={t(`add-item.allergens.${key}.title`)}
                    secondary={t(`add-item.allergens.${key}.description`)}
                  />
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Nutritional Values */}
          <Box
            onClick={() => setIsOpenNutritionalValuesModal(true)}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              border: 'solid 1px',
              borderColor: 'custom.gray400',
              borderRadius: 2,
              height: '56px',
              paddingX: 2,
              ':hover': {
                cursor: 'pointer',
                borderColor: 'custom.gray800',
              },
            }}
          >
            <Typography color="textSecondary" fontSize="14px">
              {t('menu.nutritionalValues')}
            </Typography>
            {nutritionalValues && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <img src="/assets/icons/check.svg" alt="check-icon" />
              </Box>
            )}
          </Box>
        </Box>

        <Box>
          <CustomDivider />
        </Box>
      </Box>

      {/* Modals */}
      {isOpenAgeModal && (
        <AgeRestrictionModal
          age={age || 0}
          onClose={(e: any) => {
            e?.stopPropagation();
            setIsOpenAgeModal(false);
          }}
          onSave={saveAgeRestriction}
        />
      )}

      {isOpenNutritionalValuesModal && (
        <NutritionalValuesModal
          initialValue={nutritionalValues}
          onClose={(e: any) => {
            e?.stopPropagation();
            setIsOpenNutritionalValuesModal(false);
          }}
          onSave={saveNutritionalValues}
        />
      )}
    </>
  );
};

export const ItemLibraryEdit = ({ initialValues }: EditItemModalProps) => {
  return (
    <EditDialog {...getFullscreenModalProps()} mutationMode="pessimistic">
      <SimpleForm toolbar={false} sx={{ p: 0 }}>
        <ItemLibraryEditInner />
      </SimpleForm>
    </EditDialog>
  );
};
