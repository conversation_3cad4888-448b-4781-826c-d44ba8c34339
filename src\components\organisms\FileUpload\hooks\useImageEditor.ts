import { useState, useCallback } from 'react';
import { ImageEditorConfig, AspectRatioGroup } from '~/types/fileUpload';
import { ImageEditorState, FileUploadConfig } from '../types';
import {
    getAspectRatioGroups,
    isMultiAspectRatioConfig,
    shouldEditPublicImage,
} from '~/utils/imageEditorUtils';

/**
 * Extended image editor state for multi-aspect ratio support
 */
interface ExtendedImageEditorState extends ImageEditorState {
    pendingFiles: File[];
    currentEditIndex: number;
    aspectRatioGroups: AspectRatioGroup[];
    currentAspectRatioGroup: number;
    allCroppedFiles: File[];
}

/**
 * Hook for managing image editor functionality
 */
export const useImageEditor = (
    config: FileUploadConfig,
    callbacks?: {
        onImageEdit?: (originalFile: File, editedFiles: File[]) => void;
        onImageEditCancel?: (file: File) => void;
        onImageEditStart?: (file: File) => void;
        onUploadImageWithCroppedVariants?: (originalFile: File, croppedFiles: File[]) => Promise<void>;
    }
) => {
    const [state, setState] = useState<ExtendedImageEditorState>({
        isOpen: false,
        currentFile: null,
        config: null,
        pendingFiles: [],
        currentEditIndex: 0,
        aspectRatioGroups: [],
        currentAspectRatioGroup: 0,
        allCroppedFiles: [],
    });

    // Update state helper
    const updateState = useCallback((updates: Partial<ExtendedImageEditorState>) => {
        setState(prev => ({ ...prev, ...updates }));
    }, []);

    // Check if file should be edited
    const shouldFileBeEdited = useCallback(async (file: File): Promise<boolean> => {
        if (!file.type.startsWith('image/')) return false;

        // For images and public fileTypes, check if they need editing
        if (config.fileType === 'images' || config.fileType === 'public') {
            return await shouldEditPublicImage(file, config.fileType, config.imageConfig);
        }

        // For other fileTypes, never show image editor
        return false;
    }, [config]);

    // Start editing process for multiple files
    const startEditingProcess = useCallback(async (files: File[]) => {
        // Filter files that need editing
        const imagesToEdit: File[] = [];

        for (const file of files) {
            if (await shouldFileBeEdited(file)) {
                imagesToEdit.push(file);
            }
        }

        if (imagesToEdit.length === 0) return [];

        // Initialize aspect ratio groups for multi-aspect-ratio workflow
        const isMultiAspectRatio = isMultiAspectRatioConfig(
            config.imageConfig?.editorConfig || {},
            config.fileType
        );

        let aspectRatioGroups: AspectRatioGroup[] = [];
        if (isMultiAspectRatio) {
            aspectRatioGroups = getAspectRatioGroups(
                config.imageConfig?.editorConfig || {},
                config.fileType
            );
        }

        // Set up editing state
        updateState({
            pendingFiles: imagesToEdit,
            currentEditIndex: 0,
            currentFile: imagesToEdit[0],
            isOpen: true,
            config: config.imageConfig?.editorConfig || null,
            aspectRatioGroups,
            currentAspectRatioGroup: 0,
            allCroppedFiles: [],
        });

        // Call start callback
        callbacks?.onImageEditStart?.(imagesToEdit[0]);

        return files.filter(file => !imagesToEdit.includes(file)); // Return files that don't need editing
    }, [config, shouldFileBeEdited, updateState, callbacks]);

    // Open editor for a single file
    const openEditor = useCallback((file: File, editorConfig?: ImageEditorConfig) => {
        updateState({
            isOpen: true,
            currentFile: file,
            config: editorConfig || config.imageConfig?.editorConfig || null,
            pendingFiles: [file],
            currentEditIndex: 0,
            aspectRatioGroups: [],
            currentAspectRatioGroup: 0,
            allCroppedFiles: [],
        });

        callbacks?.onImageEditStart?.(file);
    }, [config.imageConfig?.editorConfig, updateState, callbacks]);

    // Close editor
    const closeEditor = useCallback(() => {
        updateState({
            isOpen: false,
            currentFile: null,
            config: null,
            pendingFiles: [],
            currentEditIndex: 0,
            aspectRatioGroups: [],
            currentAspectRatioGroup: 0,
            allCroppedFiles: [],
        });
    }, [updateState]);

    // Handle save edits
    const saveEdits = useCallback(async (editedFiles: File[]): Promise<void> => {
        if (!state.currentFile || editedFiles.length === 0) return;

        try {
            console.log('🎨 Saving edits for:', state.currentFile.name, {
                editedFilesCount: editedFiles.length,
                currentEditIndex: state.currentEditIndex,
                totalFiles: state.pendingFiles.length,
                currentAspectRatioGroup: state.currentAspectRatioGroup,
                totalAspectRatios: state.aspectRatioGroups.length
            });

            // The image editor includes the original file as the first item
            // Separate the original file from the cropped variants
            const [, ...croppedVariants] = editedFiles;

            // Add only the cropped variants to accumulated files (not the original)
            const newAllCroppedFiles = [...state.allCroppedFiles, ...croppedVariants];
            updateState({ allCroppedFiles: newAllCroppedFiles });

            // Check if we're doing multi-aspect-ratio cropping
            const isMultiAspectRatio = isMultiAspectRatioConfig(
                config.imageConfig?.editorConfig || {},
                config.fileType
            );

            console.log('🎯 Multi-aspect-ratio check:', {
                isMultiAspectRatio,
                aspectRatioGroups: state.aspectRatioGroups,
                currentGroup: state.currentAspectRatioGroup
            });

            if (isMultiAspectRatio) {
                const nextGroupIndex = state.currentAspectRatioGroup + 1;

                if (nextGroupIndex < state.aspectRatioGroups.length) {
                    // Move to next aspect ratio group for the same image
                    console.log('➡️ Moving to next aspect ratio group:', nextGroupIndex);
                    updateState({ currentAspectRatioGroup: nextGroupIndex });
                    return; // Keep editor open for next edit
                } else {
                    // Finished all aspect ratios for current image
                    console.log('✅ Finished all aspect ratios for current image');
                    const allFilesForThisImage = newAllCroppedFiles;

                    // Call the onImageEdit callback with all files from all aspect ratios
                    callbacks?.onImageEdit?.(state.currentFile, allFilesForThisImage);

                    // Upload the original file and all cropped variants
                    if (callbacks?.onUploadImageWithCroppedVariants) {
                        await callbacks.onUploadImageWithCroppedVariants(state.currentFile, allFilesForThisImage);
                    }

                    // Reset aspect ratio state for next file
                    updateState({
                        currentAspectRatioGroup: 0,
                        allCroppedFiles: [],
                    });
                }
            } else {
                // Single aspect ratio - process normally
                console.log('📷 Single aspect ratio processing');
                callbacks?.onImageEdit?.(state.currentFile, croppedVariants);

                // Upload the original file and the cropped variants
                if (callbacks?.onUploadImageWithCroppedVariants) {
                    await callbacks.onUploadImageWithCroppedVariants(state.currentFile, croppedVariants);
                }
            }

            // Move to next image or close editor
            const nextIndex = state.currentEditIndex + 1;
            console.log('🔄 Checking next file:', {
                nextIndex,
                totalFiles: state.pendingFiles.length,
                hasNextFile: nextIndex < state.pendingFiles.length
            });

            if (nextIndex < state.pendingFiles.length) {
                const nextFile = state.pendingFiles[nextIndex];
                console.log('📁 Moving to next file:', nextFile.name);

                updateState({
                    currentEditIndex: nextIndex,
                    currentFile: nextFile,
                    currentAspectRatioGroup: 0,
                    allCroppedFiles: [],
                });

                callbacks?.onImageEditStart?.(nextFile);
            } else {
                // All images processed
                console.log('🏁 All images processed, closing editor');
                closeEditor();
            }
        } catch (error) {
            console.error('❌ Error in saveEdits for file:', state.currentFile.name, error);
            // Don't break the flow, try to continue or close gracefully
            closeEditor();
        }
    }, [state, config, callbacks, updateState, closeEditor]);

    // Handle cancel edits
    const cancelEdits = useCallback(() => {
        if (state.currentFile) {
            callbacks?.onImageEditCancel?.(state.currentFile);
        }

        // Reset multi-aspect-ratio state for current file
        updateState({
            currentAspectRatioGroup: 0,
            aspectRatioGroups: [],
            allCroppedFiles: [],
        });

        // Move to next image or close editor
        const nextIndex = state.currentEditIndex + 1;
        if (nextIndex < state.pendingFiles.length) {
            updateState({
                currentEditIndex: nextIndex,
                currentFile: state.pendingFiles[nextIndex],
            });
            callbacks?.onImageEditStart?.(state.pendingFiles[nextIndex]);
        } else {
            // All images processed (or skipped)
            closeEditor();
        }
    }, [state, callbacks, updateState, closeEditor]);

    // Get enhanced batch information for multi-file, multi-aspect-ratio workflow
    const getEnhancedBatchInfo = useCallback(() => {
        if (!state.currentFile || !state.aspectRatioGroups || state.aspectRatioGroups.length === 0) {
            // Fallback to simple batch info
            return state.pendingFiles.length > 1
                ? {
                    current: state.currentEditIndex + 1,
                    total: state.pendingFiles.length,
                }
                : undefined;
        }

        const totalFiles = state.pendingFiles.length;
        const totalAspectRatios = state.aspectRatioGroups.length;
        const fileIndex = state.currentEditIndex + 1;
        const aspectRatioIndex = state.currentAspectRatioGroup + 1;
        const aspectRatioName =
            state.aspectRatioGroups[state.currentAspectRatioGroup]?.name ||
            `Aspect Ratio ${aspectRatioIndex}`;

        // For multi-aspect-ratio workflow, calculate overall progress
        // Each file needs to go through all aspect ratios
        const overallCurrent = state.currentEditIndex * totalAspectRatios + aspectRatioIndex;
        const overallTotal = totalFiles * totalAspectRatios;

        console.log('📊 Batch info calculation:', {
            fileIndex,
            totalFiles,
            aspectRatioIndex,
            totalAspectRatios,
            overallCurrent,
            overallTotal,
            currentEditIndex: state.currentEditIndex,
            currentAspectRatioGroup: state.currentAspectRatioGroup
        });

        return {
            current: overallCurrent,
            total: overallTotal,
            fileIndex,
            totalFiles,
            aspectRatioIndex,
            totalAspectRatios,
            aspectRatioName,
        };
    }, [state]);

    // Get aspect ratio info for display
    const getAspectRatioInfo = useCallback(() => {
        if (!state.aspectRatioGroups || state.aspectRatioGroups.length <= 1) {
            return undefined;
        }

        return {
            currentGroup: state.currentAspectRatioGroup + 1,
            totalGroups: state.aspectRatioGroups.length,
            groupName: state.aspectRatioGroups[state.currentAspectRatioGroup]?.name || 'Unknown',
        };
    }, [state]);

    return {
        // State
        isOpen: state.isOpen,
        currentFile: state.currentFile,
        editorConfig: state.config,
        pendingFiles: state.pendingFiles,
        currentEditIndex: state.currentEditIndex,
        aspectRatioGroups: state.aspectRatioGroups,
        currentAspectRatioGroup: state.currentAspectRatioGroup,
        allCroppedFiles: state.allCroppedFiles,

        // Computed values
        batchInfo: getEnhancedBatchInfo(),
        aspectRatioInfo: getAspectRatioInfo(),
        hasMultipleFiles: state.pendingFiles.length > 1,
        hasMultipleAspectRatios: state.aspectRatioGroups.length > 1,
        isLastFile: state.currentEditIndex === state.pendingFiles.length - 1,
        isLastAspectRatio: state.currentAspectRatioGroup === state.aspectRatioGroups.length - 1,

        // Actions
        startEditingProcess,
        openEditor,
        closeEditor,
        saveEdits,
        cancelEdits,
        shouldFileBeEdited,
    };
};
