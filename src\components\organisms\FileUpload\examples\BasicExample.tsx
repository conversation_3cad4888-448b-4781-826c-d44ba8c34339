import React, { useState } from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { UploadedFile } from '~/types/fileUpload';
import { FileUploadComponent, createConfig } from '../index';

/**
 * Basic example of using the new FileUploadComponent
 */
export const BasicFileUploadExample: React.FC = () => {
  const [files, setFiles] = useState<UploadedFile[]>([]);

  // Example configuration for item images
  const config = createConfig.itemImages([
    { width: 400, height: 300, key: 'medium', name: 'Medium' },
    { width: 800, height: 600, key: 'large', name: 'Large' },
  ]);

  return (
    <Box sx={{ p: 3, maxWidth: 600 }}>
      <Typography variant="h5" gutterBottom>
        New File Upload Component Example
      </Typography>
      
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Item Images with Image Editor
        </Typography>
        <FileUploadComponent
          value={files}
          onChange={setFiles}
          config={config}
          label="Upload Item Images"
          helperText="Upload images for your menu item. Images will be automatically resized."
        />
      </Paper>

      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Current Files ({files.length})
        </Typography>
        {files.length === 0 ? (
          <Typography color="textSecondary">No files uploaded yet</Typography>
        ) : (
          <Box>
            {files.map((file, index) => (
              <Typography key={index} variant="body2">
                {file.fn} ({file.t === 'i' ? 'Image' : 'File'})
                {file.x && ' (Temporary)'}
              </Typography>
            ))}
          </Box>
        )}
      </Paper>
    </Box>
  );
};

/**
 * Example of using different configurations
 */
export const ConfigurationExamples: React.FC = () => {
  const [profileImage, setProfileImage] = useState<UploadedFile[]>([]);
  const [documents, setDocuments] = useState<UploadedFile[]>([]);

  return (
    <Box sx={{ p: 3, maxWidth: 800 }}>
      <Typography variant="h5" gutterBottom>
        Configuration Examples
      </Typography>

      <Box sx={{ display: 'grid', gap: 3 }}>
        {/* Profile Image */}
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Profile Image (Single, with Editor)
          </Typography>
          <FileUploadComponent
            value={profileImage}
            onChange={setProfileImage}
            config={createConfig.profileImage()}
            label="Profile Picture"
          />
        </Paper>

        {/* Documents */}
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Documents (Multiple, No Editor)
          </Typography>
          <FileUploadComponent
            value={documents}
            onChange={setDocuments}
            config={createConfig.documents()}
            label="Upload Documents"
          />
        </Paper>

        {/* Compact Variant */}
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Compact Variant
          </Typography>
          <FileUploadComponent
            value={[]}
            onChange={() => {}}
            config={{
              ...createConfig.documents(),
              ui: { variant: 'compact' },
            }}
            label="Compact Upload"
          />
        </Paper>
      </Box>
    </Box>
  );
};
