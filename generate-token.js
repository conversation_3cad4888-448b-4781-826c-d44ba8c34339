// Node.js version of the token generator utility
// Usage: node generate-token.js

// Helper functions for encoding - Simple base64 with URL-safe characters
const encodeToCustomBase = (data) => {
    // Convert to standard base64
    let binary = '';
    for (let i = 0; i < data.length; i++) {
        binary += String.fromCharCode(data[i]);
    }

    // Create base64 string and make it URL-safe
    const base64 = Buffer.from(binary, 'binary').toString('base64')
        .replace(/\+/g, '-') // Replace + with -
        .replace(/\//g, '_') // Replace / with _
        .replace(/=+$/, ''); // Remove padding =

    return base64;
};

const decodeFromCustomBase = (encoded) => {
    try {
        // Convert back from URL-safe base64
        let base64 = encoded
            .replace(/-/g, '+') // Replace - with +
            .replace(/_/g, '/'); // Replace _ with /

        // Add padding if needed
        while (base64.length % 4 !== 0) {
            base64 += '=';
        }

        // Decode from base64
        const buffer = Buffer.from(base64, 'base64');
        return new Uint8Array(buffer);
    } catch (error) {
        console.error('Failed to decode base64:', error, 'Input:', encoded);
        throw new Error('Invalid token format');
    }
};

// Simple checksum function
const calculateChecksum = (data) => {
    let checksum = 0;
    for (let i = 0; i < data.length; i++) {
        checksum = (checksum + data.charCodeAt(i)) & 0xff;
    }
    return checksum;
};

// Simple XOR obfuscation - not for security, just basic obfuscation
const simpleXorObfuscate = (data, key) => {
    if (!data || !key) {
        throw new Error('Data and key must not be empty');
    }

    // Add a simple checksum at the beginning for validation
    const checksum = calculateChecksum(data);
    const fullData = String.fromCharCode(checksum) + data;

    const result = new Uint8Array(fullData.length);

    console.log('XOR obfuscate debug:', {
        originalData: data,
        dataLength: data.length,
        keyLength: key.length,
        checksum: checksum,
        fullDataLength: fullData.length,
    });

    for (let i = 0; i < fullData.length; i++) {
        const keyChar = key.charCodeAt(i % key.length);
        result[i] = fullData.charCodeAt(i) ^ keyChar;
    }

    console.log('XOR obfuscate result:', {
        resultLength: result.length,
        result: Array.from(result),
    });

    return result;
};

const simpleXorDeobfuscate = (data, key) => {
    if (data.length < 2) {
        console.warn('XOR deobfuscate: data too short', data.length);
        return null;
    }

    if (!key) {
        console.warn('XOR deobfuscate: key is empty');
        return null;
    }

    // Deobfuscate the data
    const deobfuscated = [];
    for (let i = 0; i < data.length; i++) {
        const keyChar = key.charCodeAt(i % key.length);
        deobfuscated.push(data[i] ^ keyChar);
    }

    const fullString = String.fromCharCode(...deobfuscated);

    // Extract checksum and data
    const storedChecksum = fullString.charCodeAt(0);
    const originalData = fullString.substring(1);
    const calculatedChecksum = calculateChecksum(originalData);

    console.log('XOR deobfuscate debug:', {
        dataLength: data.length,
        deobfuscatedData: Array.from(deobfuscated),
        fullStringLength: fullString.length,
        storedChecksum: storedChecksum,
        calculatedChecksum: calculatedChecksum,
        originalData: originalData,
        checksumMatch: storedChecksum === calculatedChecksum,
    });

    // Verify checksum
    if (storedChecksum !== calculatedChecksum) {
        console.warn('XOR deobfuscate: checksum mismatch', {
            storedChecksum,
            calculatedChecksum,
        });
        return null;
    }

    return originalData;
};

/**
 * Generate an obfuscated token from account and sellpoint partials
 */
function generateObfuscatedToken(accountPartial, sellPointPartial, secret, floorPlanIndex = null, itemNumber = null) {
    try {
        if (!secret) {
            throw new Error('Secret key is required');
        }

        // Validate and format inputs
        const accountPart = accountPartial.substring(0, 12).padEnd(12, '0'); // Ensure 12 chars
        const sellPointPart = sellPointPartial.substring(0, 8).padEnd(8, '0'); // Ensure 8 chars

        // Create token string based on provided parameters
        let tokenString = accountPart + sellPointPart; // Base: 12+8 = 20 characters

        // Add floor and item parts only if provided
        if (floorPlanIndex !== null && itemNumber !== null) {
            const floorIndex = Math.min(Math.max(floorPlanIndex, 0), 99); // 0-99
            const itemNum = Math.min(Math.max(itemNumber, 0), 9999); // 0-9999
            const floorPadded = floorIndex.toString().padStart(2, '0');
            const itemPadded = itemNum.toString().padStart(4, '0');
            tokenString += floorPadded + itemPadded; // Total: 12+8+2+4 = 26 characters
        }

        console.log('Token generation debug:', {
            accountPartial: accountPart,
            sellPointPartial: sellPointPart,
            floorPlanIndex: floorPlanIndex,
            itemNumber: itemNumber,
            tokenFormat: floorPlanIndex !== null && itemNumber !== null ? 'full (26 chars)' : 'short (20 chars)',
            tokenString: tokenString,
            tokenStringLength: tokenString.length,
        });

        // Obfuscate with simple XOR
        const obfuscatedData = simpleXorObfuscate(tokenString, secret);

        // Encode with URL-safe base64
        const encodedToken = encodeToCustomBase(obfuscatedData);

        console.log('Generated token:', encodedToken);
        console.log('Token length:', encodedToken.length);

        return encodedToken;
    } catch (error) {
        console.error('Failed to generate token:', error);
        throw error;
    }
}

// Main execution
const accountPartial = '4LGfUPmHpMd3'; // 4LGfUPmHpMd31SH4dM7N
const sellPointPartial = '6EZFUg5t'; // 6EZFUg5tlcGLTytz0uUH
const secret = '637jGR3EBKycOLE';

console.log('=== TOKEN GENERATION ===');
console.log('Account Partial:', accountPartial);
console.log('SellPoint Partial:', sellPointPartial);
console.log('Secret:', secret);
console.log('');

const token = generateObfuscatedToken(accountPartial, sellPointPartial, secret);

console.log('');
console.log('=== RESULT ===');
console.log('Generated Token:', token);
console.log('Full URL would be: https://ordernow.selio.io/s/' + token);
