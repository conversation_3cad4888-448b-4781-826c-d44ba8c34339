import { useEffect, useCallback, useRef } from 'react';
import { UploadedFile } from '~/types/fileUpload';
import { inMemoryFileManager } from '~/utils/InMemoryFileManager';
import { cleanupInMemoryFile } from '~/utils/bucketManager';

/**
 * Hook for managing in-memory files with automatic cleanup and memory monitoring
 * Provides utilities for tracking, cleaning up, and monitoring in-memory file usage
 */
export function useInMemoryFileManagement() {
    const trackedFilesRef = useRef<Set<string>>(new Set());

    // Track a file for cleanup
    const trackFile = useCallback((file: UploadedFile) => {
        if (file.inMemoryData) {
            trackedFilesRef.current.add(file.fn);
        }
    }, []);

    // Clean up a specific file
    const cleanupFile = useCallback((file: UploadedFile) => {
        if (file.inMemoryData) {
            cleanupInMemoryFile(file);
            trackedFilesRef.current.delete(file.fn);
        }
    }, []);

    // Clean up multiple files
    const cleanupFiles = useCallback((files: UploadedFile[]) => {
        files.forEach(file => {
            if (file.inMemoryData) {
                cleanupInMemoryFile(file);
                trackedFilesRef.current.delete(file.fn);
            }
        });
    }, []);

    // Get current memory usage statistics
    const getMemoryStats = useCallback(() => {
        return inMemoryFileManager.getMemoryUsage();
    }, []);

    // Force cleanup of all in-memory files
    const forceCleanup = useCallback(() => {
        inMemoryFileManager.forceCleanup();
        trackedFilesRef.current.clear();
    }, []);

    // Check if memory usage is approaching limits
    const checkMemoryLimits = useCallback(() => {
        const stats = inMemoryFileManager.getMemoryUsage();
        const memoryLimitMB = 500; // 500MB limit
        const fileCountLimit = 50; // 50 files limit
        
        const warnings = [];
        
        if (stats.totalSize > memoryLimitMB * 0.8 * 1024 * 1024) { // 80% of limit
            warnings.push(`Memory usage high: ${(stats.totalSize / 1024 / 1024).toFixed(1)}MB`);
        }
        
        if (stats.fileCount > fileCountLimit * 0.8) { // 80% of limit
            warnings.push(`File count high: ${stats.fileCount} files`);
        }
        
        if (stats.blobUrlCount > 160) { // 80% of 200 blob URL limit
            warnings.push(`Blob URL count high: ${stats.blobUrlCount} URLs`);
        }
        
        return {
            warnings,
            stats,
            isNearLimit: warnings.length > 0,
        };
    }, []);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            // Clean up all tracked files on unmount
            trackedFilesRef.current.clear();
            // Note: We don't call forceCleanup here as other components might still be using files
        };
    }, []);

    // Periodic memory monitoring (optional)
    useEffect(() => {
        const interval = setInterval(() => {
            const { warnings, isNearLimit } = checkMemoryLimits();
            if (isNearLimit) {
                console.warn('In-memory file usage approaching limits:', warnings);
            }
        }, 60000); // Check every minute

        return () => clearInterval(interval);
    }, [checkMemoryLimits]);

    return {
        trackFile,
        cleanupFile,
        cleanupFiles,
        getMemoryStats,
        forceCleanup,
        checkMemoryLimits,
    };
}

/**
 * Simplified hook for basic in-memory file cleanup on component unmount
 * Use this in components that handle in-memory files to prevent memory leaks
 */
export function useInMemoryFileCleanup() {
    useEffect(() => {
        return () => {
            // Force cleanup on unmount
            inMemoryFileManager.forceCleanup();
        };
    }, []);
}

/**
 * Hook for monitoring memory usage with warnings
 * Provides real-time memory usage information and warnings
 */
export function useMemoryMonitor(options?: {
    warningThreshold?: number; // Memory threshold in MB for warnings
    onWarning?: (stats: any) => void; // Callback when warning threshold is exceeded
    checkInterval?: number; // Check interval in milliseconds
}) {
    const {
        warningThreshold = 400, // 400MB default warning threshold
        onWarning,
        checkInterval = 30000, // 30 seconds default
    } = options || {};

    const getStats = useCallback(() => {
        return inMemoryFileManager.getMemoryUsage();
    }, []);

    useEffect(() => {
        const interval = setInterval(() => {
            const stats = getStats();
            const memoryUsageMB = stats.totalSize / 1024 / 1024;
            
            if (memoryUsageMB > warningThreshold) {
                const warningData = {
                    memoryUsageMB: memoryUsageMB.toFixed(1),
                    fileCount: stats.fileCount,
                    blobUrlCount: stats.blobUrlCount,
                    threshold: warningThreshold,
                };
                
                console.warn('Memory usage warning:', warningData);
                onWarning?.(warningData);
            }
        }, checkInterval);

        return () => clearInterval(interval);
    }, [warningThreshold, onWarning, checkInterval, getStats]);

    return {
        getStats,
    };
}
