# Migration Guide: From Old to New File Upload System

This guide helps you migrate from the old `MuiFileUploadInput` and `FileUploadInput` components to the new modular file upload system.

## Quick Migration Steps

### 1. Update Imports

**Old:**
```tsx
import MuiFileUploadInput from '~/components/molecules/MuiFileUploadInput';
import FileUploadInput from '~/components/molecules/FileUploadInput';
```

**New:**
```tsx
import { FileUploadComponent, RaFileUploadComponent } from '~/components/organisms/FileUpload';
```

### 2. Convert Props to Configuration

The new system uses a single `config` object instead of multiple props.

#### Basic File Upload

**Old:**
```tsx
<MuiFileUploadInput
  value={files}
  onChange={setFiles}
  fileType="images"
  multiple={true}
  maxFiles={10}
  maxSize={5242880}
  acceptedTypes={['image/jpeg', 'image/png']}
  disabled={false}
  placeholder="Drop images here"
/>
```

**New:**
```tsx
<FileUploadComponent
  value={files}
  onChange={setFiles}
  config={{
    fileType: 'images',
    multiple: true,
    maxFiles: 10,
    maxSize: 5242880,
    acceptedTypes: ['image/jpeg', 'image/png'],
    ui: {
      disabled: false,
      placeholder: "Drop images here",
    },
  }}
/>
```

#### With Image Editor

**Old:**
```tsx
<MuiFileUploadInput
  value={files}
  onChange={setFiles}
  fileType="images"
  enableImageEditor={true}
  imageEditorConfig={{
    aspectRatios: [
      { width: 16, height: 9, name: 'Landscape' },
      { width: 1, height: 1, name: 'Square' },
    ],
  }}
  targetSizes={[
    { width: 400, height: 300, key: 'medium' },
    { width: 800, height: 600, key: 'large' },
  ]}
/>
```

**New:**
```tsx
<FileUploadComponent
  value={files}
  onChange={setFiles}
  config={{
    fileType: 'images',
    imageConfig: {
      enableEditor: true,
      editorConfig: {
        aspectRatios: [
          { width: 16, height: 9, name: 'Landscape' },
          { width: 1, height: 1, name: 'Square' },
        ],
      },
      targetSizes: [
        { width: 400, height: 300, key: 'medium' },
        { width: 800, height: 600, key: 'large' },
      ],
    },
  }}
/>
```

#### React-Admin Forms

**Old:**
```tsx
<FileUploadInput
  source="profileImage"
  fileType="images"
  multiple={false}
  enableImageEditor={true}
  validate={required()}
/>
```

**New:**
```tsx
<RaFileUploadComponent
  source="profileImage"
  config={{
    fileType: 'images',
    multiple: false,
    imageConfig: {
      enableEditor: true,
    },
  }}
  validate={required()}
/>
```

### 3. Update Callback Handlers

**Old:**
```tsx
<MuiFileUploadInput
  onUploadSuccess={(files) => console.log('Success:', files)}
  onUploadError={(error) => console.log('Error:', error)}
  onValidationError={(message) => console.log('Validation:', message)}
/>
```

**New:**
```tsx
<FileUploadComponent
  config={{
    // ... other config
    callbacks: {
      onUploadSuccess: (files) => console.log('Success:', files),
      onUploadError: (error) => console.log('Error:', error),
      onValidationError: (errors) => console.log('Validation:', errors),
    },
  }}
/>
```

## Configuration Mapping

| Old Prop | New Config Path | Notes |
|----------|-----------------|-------|
| `fileType` | `config.fileType` | Same values |
| `multiple` | `config.multiple` | Same |
| `maxFiles` | `config.maxFiles` | Same |
| `maxSize` | `config.maxSize` | Same |
| `acceptedTypes` | `config.acceptedTypes` | Same |
| `enableImageEditor` | `config.imageConfig.enableEditor` | Boolean |
| `imageEditorConfig` | `config.imageConfig.editorConfig` | Same object |
| `targetSizes` | `config.imageConfig.targetSizes` | Same array |
| `quality` | `config.imageConfig.quality` | Same |
| `disabled` | `config.ui.disabled` | Boolean |
| `readOnly` | `config.ui.readOnly` | Boolean |
| `placeholder` | `config.ui.placeholder` | String |
| `variant` | `config.ui.variant` | 'default' or 'compact' |
| `privateFileContext` | `config.privateContext` | Same object |
| `onUploadSuccess` | `config.callbacks.onUploadSuccess` | Function |
| `onUploadError` | `config.callbacks.onUploadError` | Function |
| `onValidationError` | `config.callbacks.onValidationError` | Function |

## Using Configuration Presets

The new system includes helpful configuration presets:

```tsx
import { createConfig } from '~/components/organisms/FileUpload';

// Item images with editor
const itemConfig = createConfig.itemImages([
  { width: 400, height: 300, key: 'medium' },
  { width: 800, height: 600, key: 'large' },
]);

// Profile image
const profileConfig = createConfig.profileImage();

// Documents
const documentConfig = createConfig.documents();

// Private documents
const privateConfig = createConfig.privateDocuments(privateContext);

// Videos
const videoConfig = createConfig.videos();
```

## Advanced Migration Examples

### Complex Configuration

**Old:**
```tsx
<MuiFileUploadInput
  value={files}
  onChange={setFiles}
  fileType="images"
  multiple={true}
  maxFiles={5}
  maxSize={10485760}
  acceptedTypes={['image/jpeg', 'image/png', 'image/webp']}
  enableImageEditor={true}
  imageEditorConfig={{
    aspectRatios: [
      { width: 16, height: 9, name: 'Landscape' },
      { width: 4, height: 3, name: 'Standard' },
      { width: 1, height: 1, name: 'Square' },
    ],
    quality: 0.9,
    allowRotation: true,
    allowZoom: true,
  }}
  targetSizes={[
    { width: 200, height: 150, key: 'thumbnail' },
    { width: 800, height: 600, key: 'display' },
    { width: 1200, height: 900, key: 'large' },
  ]}
  minFileCount={1}
  customValidation={(file) => {
    if (file.size < 1024) return 'File too small';
    return null;
  }}
  onUploadSuccess={(files) => {
    console.log('Uploaded files:', files);
    updateAnalytics('files_uploaded', files.length);
  }}
  onValidationError={(message) => {
    showNotification(message, 'warning');
  }}
  disabled={isSubmitting}
  placeholder="Drop high-quality images here"
/>
```

**New:**
```tsx
<FileUploadComponent
  value={files}
  onChange={setFiles}
  config={{
    fileType: 'images',
    multiple: true,
    maxFiles: 5,
    maxSize: 10485760,
    acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    
    imageConfig: {
      enableEditor: true,
      editorConfig: {
        aspectRatios: [
          { width: 16, height: 9, name: 'Landscape' },
          { width: 4, height: 3, name: 'Standard' },
          { width: 1, height: 1, name: 'Square' },
        ],
        quality: 0.9,
        allowRotation: true,
        allowZoom: true,
      },
      targetSizes: [
        { width: 200, height: 150, key: 'thumbnail' },
        { width: 800, height: 600, key: 'display' },
        { width: 1200, height: 900, key: 'large' },
      ],
    },
    
    validation: {
      minFileCount: 1,
      customValidation: (file) => {
        if (file.size < 1024) return 'File too small';
        return null;
      },
    },
    
    ui: {
      disabled: isSubmitting,
      placeholder: "Drop high-quality images here",
    },
    
    callbacks: {
      onUploadSuccess: (files) => {
        console.log('Uploaded files:', files);
        updateAnalytics('files_uploaded', files.length);
      },
      onValidationError: (errors) => {
        const message = errors[0]?.message || 'Validation error';
        showNotification(message, 'warning');
      },
    },
  }}
/>
```

## Testing Your Migration

1. **Functionality Test**: Ensure all upload flows work as expected
2. **Validation Test**: Check that file validation still works correctly
3. **Image Editor Test**: Verify image editing and cropping functionality
4. **Error Handling Test**: Test error scenarios and user feedback
5. **Performance Test**: Check upload performance and progress indicators

## Rollback Plan

If you need to rollback:

1. Keep the old components available during migration
2. Use feature flags to switch between old and new systems
3. Test thoroughly in staging before production deployment
4. Have monitoring in place to catch any issues

## Benefits After Migration

- ✅ **Better Performance**: Smaller, optimized components
- ✅ **Easier Maintenance**: Modular, focused code
- ✅ **Better Testing**: Each piece can be tested independently
- ✅ **Type Safety**: Improved TypeScript support
- ✅ **Flexibility**: Easy to customize and extend
- ✅ **Developer Experience**: Much easier to understand and modify
