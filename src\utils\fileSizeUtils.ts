import { getMetadata, ref } from 'firebase/storage';
import { UploadedFile, PrivateFileContext } from '~/types/fileUpload';
import { generateFileUrl } from './urlGeneration';
import { getStorageInstanceForFile } from './bucketManager';
import { getDestinationPath } from './urlGeneration';

/**
 * Cache for file sizes to avoid repeated requests
 * Enhanced with localStorage persistence for cross-session caching
 */
const fileSizeCache = new Map<string, number>();
const cacheTTL = 30 * 60 * 1000; // 30 minutes
const cacheTimestamps = new Map<string, number>();

// LocalStorage keys for persistent caching
const CACHE_STORAGE_KEY = 'selio_file_size_cache';
const CACHE_TIMESTAMPS_KEY = 'selio_file_size_timestamps';

/**
 * Load cache from localStorage on initialization
 */
const loadCacheFromStorage = (): void => {
    if (typeof window === 'undefined') return;

    try {
        const cachedSizes = localStorage.getItem(CACHE_STORAGE_KEY);
        const cachedTimestamps = localStorage.getItem(CACHE_TIMESTAMPS_KEY);

        if (cachedSizes && cachedTimestamps) {
            const sizes = JSON.parse(cachedSizes);
            const timestamps = JSON.parse(cachedTimestamps);

            // Only load entries that haven't expired
            const now = Date.now();
            Object.entries(timestamps).forEach(([key, timestamp]) => {
                if (now - (timestamp as number) < cacheTTL && sizes[key]) {
                    fileSizeCache.set(key, sizes[key]);
                    cacheTimestamps.set(key, timestamp as number);
                }
            });

        }
    } catch (error) {
        // Failed to load cache from localStorage
    }
};

/**
 * Save cache to localStorage
 */
const saveCacheToStorage = (): void => {
    if (typeof window === 'undefined') return;

    try {
        const sizes = Object.fromEntries(fileSizeCache);
        const timestamps = Object.fromEntries(cacheTimestamps);

        localStorage.setItem(CACHE_STORAGE_KEY, JSON.stringify(sizes));
        localStorage.setItem(CACHE_TIMESTAMPS_KEY, JSON.stringify(timestamps));
    } catch (error) {
        // Failed to save cache to localStorage
    }
};

// Load cache on module initialization
loadCacheFromStorage();

/**
 * Generate cache key for a file
 */
const getFileSizeKey = (file: UploadedFile): string => {
    return `${file.fn}.${file.e}${file.x ? '_temp' : ''}`;
};

/**
 * Check if cached size is still valid
 */
const isCacheValid = (key: string): boolean => {
    const timestamp = cacheTimestamps.get(key);
    if (!timestamp) return false;
    return Date.now() - timestamp < cacheTTL;
};

/**
 * Cache file size with timestamp and persist to localStorage
 */
const cacheFileSize = (key: string, size: number): void => {
    fileSizeCache.set(key, size);
    cacheTimestamps.set(key, Date.now());

    // Debounced save to localStorage to avoid excessive writes
    debouncedSaveToStorage();
};

// Debounced save function to avoid excessive localStorage writes
let saveTimeout: NodeJS.Timeout | null = null;
const debouncedSaveToStorage = (): void => {
    if (saveTimeout) clearTimeout(saveTimeout);
    saveTimeout = setTimeout(() => {
        saveCacheToStorage();
        saveTimeout = null;
    }, 1000); // Save after 1 second of inactivity
};

/**
 * Get file size from Firebase Storage metadata or in-memory data
 * Used for both temporary and permanent files
 */
export const getFileSize = async (
    file: UploadedFile,
    context?: PrivateFileContext
): Promise<number> => {
    // For in-memory files, get size directly from metadata
    if (file.inMemoryData) {
        return file.inMemoryData.metadata.fileSize;
    }

    const cacheKey = getFileSizeKey(file);

    // Check cache first
    if (isCacheValid(cacheKey)) {
        const cachedSize = fileSizeCache.get(cacheKey);
        if (cachedSize !== undefined) {
            return cachedSize;
        }
    }

    try {
        let fileRef;

        // Check if this is a folder-based public image (client-side processed)
        // Detect by: type is 'i', and URL contains the folder pattern, OR if we know it's moved to folder structure
        const isFolderBasedImage = file.t === 'i' && (
            (file.url && (file.url.includes(`i%2F${file.fn}%2F`) || file.url.includes(`i/${file.fn}/`))) ||
            // Also check if this is a newer upload that should be in folder structure
            (!file.x && file.t === 'i') // Non-temp public image should be in folder structure
        );

        if (isFolderBasedImage) {
            // For folder-based public images, get the size of the ORIGINAL file (not thumbnail)
            // This represents the actual uploaded file size, not the processed thumbnail
            const storage = getStorageInstanceForFile(file.t, file.x || false);
            const originalPath = `i/${file.fn}/original.${file.e}`;
            fileRef = ref(storage, originalPath);
        } else if (file.x) {
            // Traditional temporary file
            const storage = getStorageInstanceForFile(file.t, true);
            const filename = `${file.fn}.${file.e}`;
            fileRef = ref(storage, filename);
        } else {
            // Permanent file - use destination path logic
            const storage = getStorageInstanceForFile(file.t, false);
            const filePath = getDestinationPath(file, context);
            fileRef = ref(storage, filePath);
        }

        const metadata = await getMetadata(fileRef);
        const size = metadata.size;

        // Cache the result
        cacheFileSize(cacheKey, size);

        return size;
    } catch (error) {
        // Fallback: try HTTP HEAD request if we can generate a URL
        try {
            const url = await generateFileUrl(file, { context });
            return await getFileSizeFromUrl(url);
        } catch (urlError) {
            throw new Error('Unable to determine file size');
        }
    }
};

/**
 * Batch file size loading for improved performance
 * Loads multiple file sizes in parallel with intelligent batching
 */
export const getBatchFileSizes = async (
    files: UploadedFile[],
    context?: PrivateFileContext
): Promise<Map<string, number>> => {
    const results = new Map<string, number>();
    const filesToLoad: UploadedFile[] = [];

    // First pass: check cache and in-memory data for all files
    for (const file of files) {
        const cacheKey = getFileSizeKey(file);

        // For in-memory files, get size directly from metadata
        if (file.inMemoryData) {
            results.set(cacheKey, file.inMemoryData.metadata.fileSize);
            continue;
        }

        // Check cache for non-in-memory files
        if (isCacheValid(cacheKey)) {
            const cachedSize = fileSizeCache.get(cacheKey);
            if (cachedSize !== undefined) {
                results.set(cacheKey, cachedSize);
                continue;
            }
        }
        filesToLoad.push(file);
    }

    // Second pass: load uncached files in parallel batches
    const BATCH_SIZE = 10; // Limit concurrent requests
    for (let i = 0; i < filesToLoad.length; i += BATCH_SIZE) {
        const batch = filesToLoad.slice(i, i + BATCH_SIZE);

        const batchPromises = batch.map(async (file) => {
            try {
                const size = await getFileSize(file, context);
                const cacheKey = getFileSizeKey(file);
                results.set(cacheKey, size);
                return { file, size, success: true };
            } catch (error) {
                return { file, size: 0, success: false };
            }
        });

        await Promise.allSettled(batchPromises);
    }

    return results;
};

/**
 * Get file size for a specific image variant using Firebase Storage metadata or in-memory data
 * This avoids CORS issues with HEAD requests by using Firebase SDK directly
 */
export const getVariantFileSize = async (
    file: UploadedFile,
    variant: string,
    context?: PrivateFileContext
): Promise<number> => {
    // For in-memory files, get size directly from variant blob or metadata
    if (file.inMemoryData) {
        if (variant === 'original') {
            return file.inMemoryData.original.size;
        }

        // Check if variant exists in memory
        if (file.inMemoryData.variants?.has(variant)) {
            return file.inMemoryData.variants.get(variant)!.size;
        }

        // Check metadata for variant size
        const variantMetadata = file.inMemoryData.metadata.imageMetadata?.variants?.find(v => v.key === variant);
        if (variantMetadata) {
            return variantMetadata.size;
        }

        // Variant not found, throw error
        throw new Error(`Variant '${variant}' not found for in-memory file`);
    }

    const cacheKey = getFileSizeKey(file) + `_${variant}`;

    // Check cache first
    if (isCacheValid(cacheKey)) {
        const cachedSize = fileSizeCache.get(cacheKey);
        if (cachedSize !== undefined) {
            return cachedSize;
        }
    }

    try {
        // For image variants, construct the Firebase Storage path directly
        const storage = getStorageInstanceForFile(file.t, file.x || false);

        // Determine the file extension for the variant
        // Original keeps the original extension, others typically use webp
        const variantExtension = variant === 'original' ? file.e : 'webp';

        // Construct the variant path: i/{fileId}/{variant}.{extension}
        const variantPath = `i/${file.fn}/${variant}.${variantExtension}`;
        const fileRef = ref(storage, variantPath);

        const metadata = await getMetadata(fileRef);
        const size = metadata.size;

        // Cache the result
        cacheFileSize(cacheKey, size);

        return size;
    } catch (error) {
        throw error;
    }
};

/**
 * Get file size from HTTP HEAD request
 * Fallback method when Firebase metadata is unavailable
 * Note: Does not work with blob URLs (blob: protocol doesn't support HEAD requests)
 */
export const getFileSizeFromUrl = async (url: string): Promise<number> => {
    // Blob URLs don't support HEAD requests
    if (url.startsWith('blob:')) {
        throw new Error('Cannot get file size from blob URL using HEAD request');
    }

    try {
        const response = await fetch(url, { method: 'HEAD' });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const contentLength = response.headers.get('content-length');
        if (!contentLength) {
            throw new Error('Content-Length header not found');
        }

        return parseInt(contentLength, 10);
    } catch (error) {
        throw error;
    }
};

/**
 * Get file size for local File object (during upload)
 */
export const getLocalFileSize = (file: File): number => {
    return file.size;
};

/**
 * Format file size for display (enhanced version with more precision options)
 */
export const formatFileSize = (bytes: number, precision: number = 2): string => {
    if (bytes === 0) return '0 Bytes';
    if (bytes < 0) return 'Unknown';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    const value = bytes / Math.pow(k, i);
    const formatted = i === 0 ? value.toString() : value.toFixed(precision);

    return `${formatted} ${sizes[i]}`;
};

/**
 * Convert bytes to KB (for backward compatibility)
 * @deprecated Use formatFileSize instead
 */
export const bytesToKB = (bytes: number): number => {
    return Math.round(bytes / 1024);
};

/**
 * Clear file size cache (useful for testing or memory management)
 * Also clears localStorage cache
 */
export const clearFileSizeCache = (): void => {
    fileSizeCache.clear();
    cacheTimestamps.clear();

    // Clear localStorage cache as well
    if (typeof window !== 'undefined') {
        try {
            localStorage.removeItem(CACHE_STORAGE_KEY);
            localStorage.removeItem(CACHE_TIMESTAMPS_KEY);
        } catch (error) {
            // Failed to clear localStorage cache
        }
    }

};

/**
 * Get cache statistics (for debugging)
 */
export const getFileSizeCacheStats = () => {
    const now = Date.now();
    const validEntries = Array.from(cacheTimestamps.entries()).filter(
        ([_, timestamp]) => now - timestamp < cacheTTL
    ).length;

    return {
        totalEntries: fileSizeCache.size,
        validEntries,
        expiredEntries: fileSizeCache.size - validEntries
    };
};
