import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { AttachFile as AttachFileIcon } from '@mui/icons-material';
import {
  Box,
  Chip,
  CircularProgress,
  InputAdornment,
  Paper,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { FilePreviewModal } from '~/components/atoms/FilePreviewModal';
import { ImageEditorModal } from '~/components/molecules/ImageEditorModal';
import { useInMemoryFileCleanup } from '~/hooks/useInMemoryFileManagement';
import { canPreviewFile } from '~/utils/fileUtils';
import { FileDropzone } from './components/FileDropzone/FileDropzone';
import { FileList } from './components/FileList/FileList';
import { ValidationDisplay } from './components/FileValidation/ValidationDisplay';
import { useFileList } from './hooks/useFileList';
import { useFileUpload } from './hooks/useFileUpload';
import { useFileValidation } from './hooks/useFileValidation';
import { useImageEditor } from './hooks/useImageEditor';
import { FileUploadComponentProps } from './types';
import { mergeWithDefaults, validateConfig } from './utils/fileUploadConfig';

/**
 * Main FileUploadComponent - replaces MuiFileUploadInput with modular architecture
 */
const FileUploadComponent: React.FC<FileUploadComponentProps> = ({
  value = [],
  onChange,
  config: userConfig,
  label,
  helperText,
  error = false,
  errorText,
}) => {
  const theme = useTheme();
  const { t } = useTranslation();

  // Merge user config with defaults
  const config = useMemo(() => mergeWithDefaults(userConfig), [userConfig]);

  // Validate configuration
  const configErrors = useMemo(() => validateConfig(config), [config]);

  // State
  const [previewFile, setPreviewFile] = useState<any>(null);
  const [isFileListExpanded, setIsFileListExpanded] = useState(false);

  // Initialize hooks
  const fileList = useFileList(value, onChange);
  const validation = useFileValidation(config);
  const upload = useFileUpload(config);

  // Clean up in-memory files on unmount
  useInMemoryFileCleanup();

  // Update image editor with correct callback
  const imageEditorWithCallback = useImageEditor(config, {
    onImageEdit: config.callbacks?.onImageEdit,
    onImageEditCancel: config.callbacks?.onImageEditCancel,
    onImageEditStart: config.callbacks?.onImageEditStart,
    onUploadImageWithCroppedVariants: async (
      originalFile: File,
      croppedFiles: File[]
    ): Promise<void> => {
      const uploadedFile = await upload.uploadImageWithCroppedVariants(
        originalFile,
        croppedFiles
      );
      fileList.addFile(uploadedFile);
    },
  });

  // Handle file selection
  const handleFilesSelected = useCallback(
    async (selectedFiles: File[]) => {
      if (selectedFiles.length === 0) return;

      // Validate files and get errors synchronously
      const validationErrors = validation.validate(
        selectedFiles,
        fileList.files.map(() => ({}) as File)
      );

      // Check for validation errors using the returned errors, not the state
      const hasValidationErrors = validationErrors.some(
        error => error.severity === 'error'
      );

      if (hasValidationErrors && !config.validation?.allowInvalidFiles) {
        return; // Stop if there are errors and invalid files are not allowed
      }

      // Get valid files by filtering out files with errors
      const errorFiles = new Set(
        validationErrors
          .filter(error => error.severity === 'error' && error.file)
          .map(error => error.file!)
      );

      const validFiles = config.validation?.allowInvalidFiles
        ? selectedFiles
        : selectedFiles.filter(file => !errorFiles.has(file));

      if (validFiles.length === 0) return;

      try {
        // Check if any files need image editing
        const filesToEdit = await Promise.all(
          validFiles.map(async file => ({
            file,
            needsEditing:
              await imageEditorWithCallback.shouldFileBeEdited(file),
          }))
        );

        const imagesToEdit = filesToEdit
          .filter(f => f.needsEditing)
          .map(f => f.file);
        const filesToUploadDirectly = filesToEdit
          .filter(f => !f.needsEditing)
          .map(f => f.file);

        // Upload files that don't need editing
        if (filesToUploadDirectly.length > 0) {
          const uploadedFiles = await upload.upload(filesToUploadDirectly);
          fileList.addFiles(uploadedFiles);
        }

        // Start editing process for images that need editing
        if (imagesToEdit.length > 0) {
          await imageEditorWithCallback.startEditingProcess(imagesToEdit);
        }

        // Input will be reset automatically by the FileDropzone component
      } catch (error) {
        console.error('Error handling file selection:', error);
      }
    },
    [config, validation, fileList, upload, imageEditorWithCallback]
  );

  // Handle file removal
  const handleRemoveFile = useCallback(
    (index: number) => {
      const fileToRemove = fileList.files[index];
      fileList.removeFile(index);

      // Call lifecycle callback
      if (fileToRemove) {
        config.callbacks?.onFileDeleted?.(fileToRemove);
      }

      // Close expanded list if only one file or no files remain
      if (fileList.files.length <= 2) {
        // Will be 1 after removal
        setIsFileListExpanded(false);
      }
    },
    [fileList, config.callbacks]
  );

  // Handle file preview
  const handleFilePreview = useCallback((file: any) => {
    setPreviewFile(file);
  }, []);

  // Handle close preview
  const handleClosePreview = useCallback(() => {
    setPreviewFile(null);
  }, []);

  // Generate computed helper text
  const computedHelperText = useMemo(() => {
    if (helperText) return helperText;

    // Simple helper text generation
    const fileTypeText = config.fileType === 'images' ? 'images' : 'files';
    const multipleText = config.multiple
      ? `up to ${config.maxFiles} ${fileTypeText}`
      : `a ${fileTypeText.slice(0, -1)}`;
    return `Select ${multipleText}`;
  }, [config, helperText]);

  // Show configuration errors in development
  useEffect(() => {
    if (configErrors.length > 0 && process.env.NODE_ENV === 'development') {
      console.warn('FileUploadComponent configuration errors:', configErrors);
    }
  }, [configErrors]);

  // Close expanded file list when files count becomes 1 or 0
  useEffect(() => {
    if (fileList.fileCount <= 1) {
      setIsFileListExpanded(false);
    }
  }, [fileList.fileCount]);

  // Handle keyboard events and click outside for compact variant dropdown
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFileListExpanded) {
        setIsFileListExpanded(false);
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (isFileListExpanded) {
        const target = event.target as Element;
        if (!target.closest('[data-file-dropdown]')) {
          setIsFileListExpanded(false);
        }
      }
    };

    if (isFileListExpanded) {
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isFileListExpanded]);

  // Handle toggle of file list expansion
  const handleToggleFileList = useCallback(() => {
    setIsFileListExpanded(prev => !prev);
  }, []);

  // Render compact variant
  if (config.ui?.variant === 'compact') {
    return (
      <Box sx={{ width: '100%', position: 'relative' }}>
        <TextField
          fullWidth
          variant="outlined"
          label={label}
          data-file-dropdown
          placeholder={
            fileList.isEmpty
              ? config.ui?.placeholder ||
                (config.multiple
                  ? t('menu.selectFiles', 'Select files')
                  : t('menu.selectFile', 'Select file'))
              : undefined
          }
          value=""
          disabled={config.ui?.disabled || upload.uploading}
          error={error}
          helperText={errorText || computedHelperText}
          slotProps={{
            input: {
              readOnly: true,
              startAdornment: fileList.hasFiles ? (
                <InputAdornment
                  position="start"
                  sx={{
                    maxWidth: 'calc(100% - 60px)',
                    width: 'calc(100% - 60px)',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      width: '100%',
                      overflow: 'hidden',
                      minHeight: 36,
                      py: 0.5,
                    }}
                  >
                    {fileList.fileCount === 1 ? (
                      // Single file: Show file chip (regardless of multiple setting)
                      fileList.files.map((file, index) => {
                        const fileName = file.rn || file.fn;
                        const extension = file.e ? `.${file.e}` : '';

                        // Function to truncate from middle if needed
                        const getTruncatedLabel = (
                          name: string,
                          ext: string
                        ) => {
                          const fullName = `${name}${ext}`;
                          // If the full name is short enough, return as is
                          if (fullName.length <= 20) {
                            return fullName;
                          }

                          // For longer names, show first part + ... + extension
                          const maxStartLength = Math.max(
                            8,
                            20 - ext.length - 3
                          ); // 3 for "..."
                          const truncatedName =
                            name.length > maxStartLength
                              ? `${name.substring(0, maxStartLength)}...${ext}`
                              : fullName;

                          return truncatedName;
                        };

                        const displayLabel = getTruncatedLabel(
                          fileName,
                          extension
                        );
                        const fullName = `${fileName}${extension}`;

                        return (
                          <Chip
                            key={`${file.fn || file.rn}-${index}`}
                            label={displayLabel}
                            title={fullName} // Show full name on hover
                            onDelete={
                              !config.ui?.disabled && !config.ui?.readOnly
                                ? () => handleRemoveFile(index)
                                : undefined
                            }
                            onClick={
                              canPreviewFile(file) && !config.ui?.disabled
                                ? () => handleFilePreview(file)
                                : undefined
                            }
                            size="small"
                            variant="outlined"
                            sx={{
                              cursor:
                                canPreviewFile(file) && !config.ui?.disabled
                                  ? 'pointer'
                                  : 'default',
                              backgroundColor: 'background.paper',
                              border: '1px solid',
                              borderColor: 'divider',
                              maxWidth: '100%',
                              height: '32px',
                              '& .MuiChip-label': {
                                fontWeight: 500,
                                fontSize: '0.875rem',
                                textAlign: 'left',
                                paddingLeft: '8px',
                                paddingRight: '8px',
                              },
                              '&:hover': !config.ui?.disabled
                                ? {
                                    backgroundColor: 'action.hover',
                                    borderColor: 'primary.main',
                                  }
                                : {},
                            }}
                          />
                        );
                      })
                    ) : (
                      // Multiple files: Show summary chip with count
                      <Chip
                        label={`${fileList.fileCount} file${fileList.fileCount !== 1 ? 's' : ''} uploaded`}
                        onClick={
                          !config.ui?.disabled && fileList.fileCount > 1
                            ? handleToggleFileList
                            : undefined
                        }
                        size="medium"
                        variant="outlined"
                        sx={{
                          cursor:
                            !config.ui?.disabled && fileList.fileCount > 1
                              ? 'pointer'
                              : 'default',
                          minHeight: 32,
                          height: 32,
                          fontSize: '0.875rem',
                          backgroundColor: 'background.paper',
                          border: '1px solid',
                          borderColor: 'divider',
                          '& .MuiChip-label': {
                            fontWeight: 500,
                            paddingX: 1.5,
                            fontSize: '0.875rem',
                          },
                          '&:hover':
                            !config.ui?.disabled && fileList.fileCount > 1
                              ? {
                                  backgroundColor: 'action.hover',
                                  borderColor: 'primary.main',
                                }
                              : {},
                        }}
                      />
                    )}
                  </Box>
                </InputAdornment>
              ) : undefined,
              endAdornment: !config.ui?.readOnly ? (
                <InputAdornment position="end">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    {/* File input and browse icon */}
                    <FileDropzone
                      variant="compact"
                      onFilesSelected={handleFilesSelected}
                      disabled={config.ui?.disabled || upload.uploading}
                      multiple={config.multiple}
                      acceptedTypes={config.acceptedTypes}
                      maxFiles={config.maxFiles}
                      uploading={upload.uploading}
                    >
                      {upload.uploading ? (
                        <CircularProgress size={20} />
                      ) : (
                        <AttachFileIcon
                          fontSize="small"
                          sx={{
                            cursor:
                              config.ui?.disabled || upload.uploading
                                ? 'not-allowed'
                                : 'pointer',
                            color: config.ui?.disabled
                              ? 'action.disabled'
                              : 'action.active',
                          }}
                        />
                      )}
                    </FileDropzone>
                  </Box>
                </InputAdornment>
              ) : undefined,
            },
            inputLabel: {
              shrink: fileList.hasFiles || undefined,
            },
          }}
          sx={{
            cursor: 'default',
            '& .MuiOutlinedInput-input': {
              cursor: 'default',
              display: 'none', // Hide the actual input since we're using chips
            },
            '& .MuiOutlinedInput-root': {
              cursor: 'default',
              paddingRight: '14px',
              paddingLeft: '14px',
              // Add focus styling when dropdown is open
              ...(isFileListExpanded && {
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: theme.palette.primary.main,
                  borderWidth: '2px',
                },
              }),
            },
            '& .MuiInputAdornment-positionStart': {
              maxHeight: 'none',
              alignItems: 'stretch',
            },
            '& .MuiInputAdornment-positionEnd': {
              position: 'absolute',
              right: '9px',
              top: '50%',
              transform: 'translateY(-50%)',
              pointerEvents: 'none',
              '& > *': {
                pointerEvents: 'auto',
              },
            },
            // Style the label when dropdown is open (like focused state)
            ...(isFileListExpanded && {
              '& .MuiInputLabel-root': {
                color: theme.palette.primary.main,
              },
            }),
          }}
        />

        {/* Expandable file list for multiple files - Overlay dropdown style */}
        {config.multiple && fileList.fileCount > 1 && isFileListExpanded && (
          <Box
            data-file-dropdown
            sx={{
              position: 'absolute',
              top: 'calc(100% - 3px)', // Move up by 1px to overlap the input border
              left: 0,
              right: 0,
              zIndex: 1300, // Higher than most components
              display: 'flex',
              flexDirection: 'column',
              gap: 0.5,
              maxHeight: '200px',
              overflowY: 'auto',
              overflowX: 'hidden',
              p: 1,
              border: `1px solid ${theme.palette.divider}`,
              borderTop: 'none', // Remove top border to connect with input
              borderRadius: '4px',
              backgroundColor: theme.palette.background.paper,
              boxShadow: theme.shadows[8], // Add elevation like a dropdown
              animation: 'fadeIn 0.2s ease-in-out',
              '@keyframes fadeIn': {
                from: {
                  opacity: 0,
                  transform: 'translateY(-10px)',
                },
                to: {
                  opacity: 1,
                  transform: 'translateY(0)',
                },
              },
            }}
          >
            <FileList
              files={fileList.files}
              onRemoveFile={handleRemoveFile}
              onReorderFiles={
                config.multiple ? fileList.reorderFiles : undefined
              }
              onFilePreview={handleFilePreview}
              disabled={config.ui?.disabled}
              readOnly={config.ui?.readOnly}
              multiple={config.multiple}
              variant="compact"
              privateFileContext={config.privateContext}
              showSummary={false}
            />
          </Box>
        )}

        {/* Validation display */}
        <ValidationDisplay
          errors={validation.errors}
          warnings={validation.warnings}
          onDismissError={validation.removeValidationError}
        />

        {/* Modals */}
        <FilePreviewModal file={previewFile} onClose={handleClosePreview} />
        <ImageEditorModal
          file={imageEditorWithCallback.currentFile}
          isOpen={imageEditorWithCallback.isOpen}
          onClose={imageEditorWithCallback.closeEditor}
          onSave={imageEditorWithCallback.saveEdits}
          onCancel={imageEditorWithCallback.cancelEdits}
          config={imageEditorWithCallback.editorConfig || {}}
          fileType={config.fileType}
          uploading={upload.uploading}
          batchInfo={imageEditorWithCallback.batchInfo}
          aspectRatioInfo={imageEditorWithCallback.aspectRatioInfo}
        />
      </Box>
    );
  }

  // Render default variant
  return (
    <Box sx={{ width: '100%' }}>
      {/* Upload Area - Hidden in disabled and readOnly modes */}
      {!config.ui?.disabled && !config.ui?.readOnly && (
        <FileDropzone
          onFilesSelected={handleFilesSelected}
          disabled={config.ui?.disabled || upload.uploading}
          multiple={config.multiple}
          acceptedTypes={config.acceptedTypes}
          maxFiles={config.maxFiles}
          placeholder={config.ui?.placeholder}
          error={error}
          uploading={upload.uploading}
        />
      )}

      {/* File List */}
      <FileList
        files={fileList.files}
        onRemoveFile={handleRemoveFile}
        onReorderFiles={config.multiple ? fileList.reorderFiles : undefined}
        onFilePreview={handleFilePreview}
        disabled={config.ui?.disabled}
        readOnly={config.ui?.readOnly}
        multiple={config.multiple}
        variant="default"
        privateFileContext={config.privateContext}
      />

      {/* Show "No files uploaded" message in disabled and readOnly modes */}
      {fileList.isEmpty && (config.ui?.disabled || config.ui?.readOnly) && (
        <Box sx={{ mt: 0 }}>
          <Paper
            variant="outlined"
            sx={{
              p: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              opacity: config.ui?.disabled ? 0.6 : 1,
              backgroundColor:
                config.ui?.disabled || config.ui?.readOnly
                  ? theme.palette.action.hover
                  : 'transparent',
              minHeight: 56,
            }}
          >
            <Typography
              variant="body2"
              color="textSecondary"
              sx={{ fontStyle: 'italic' }}
            >
              {t('menu.noFilesUploaded', 'No files uploaded')}
            </Typography>
          </Paper>
        </Box>
      )}

      {/* Show external error text */}
      {errorText && (
        <Typography
          variant="caption"
          color="error"
          sx={{ mt: 1, display: 'block' }}
        >
          {errorText}
        </Typography>
      )}

      {/* Helper text */}
      {computedHelperText && (
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mt: 1, display: 'block' }}
        >
          {computedHelperText}
        </Typography>
      )}

      {/* Validation display */}
      <ValidationDisplay
        errors={validation.errors}
        warnings={validation.warnings}
        onDismissError={validation.removeValidationError}
      />

      {/* Modals */}
      <FilePreviewModal file={previewFile} onClose={handleClosePreview} />
      <ImageEditorModal
        file={imageEditorWithCallback.currentFile}
        isOpen={imageEditorWithCallback.isOpen}
        onClose={imageEditorWithCallback.closeEditor}
        onSave={imageEditorWithCallback.saveEdits}
        onCancel={imageEditorWithCallback.cancelEdits}
        config={imageEditorWithCallback.editorConfig || {}}
        fileType={config.fileType}
        uploading={upload.uploading}
        batchInfo={imageEditorWithCallback.batchInfo}
        aspectRatioInfo={imageEditorWithCallback.aspectRatioInfo}
      />
    </Box>
  );
};

export default FileUploadComponent;
