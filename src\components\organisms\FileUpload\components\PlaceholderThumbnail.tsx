import React from 'react';
import { Box, Typography } from '@mui/material';
import { UploadedFile } from '~/types/fileUpload';
import { getFilePlaceholder, shouldUsePlaceholder } from '../utils/filePlaceholders';

interface PlaceholderThumbnailProps {
  file: UploadedFile | File;
  size?: number;
  onClick?: () => void;
  disabled?: boolean;
}

/**
 * Placeholder thumbnail component for files that don't have real thumbnails
 */
export const PlaceholderThumbnail: React.FC<PlaceholderThumbnailProps> = ({
  file,
  size = 40,
  onClick,
  disabled = false,
}) => {
  // Determine file type and extension
  const fileType = 'type' in file ? file.type : '';
  const extension = 'e' in file ? file.e : (file as File).name.split('.').pop() || '';
  
  const placeholder = getFilePlaceholder(fileType, extension);
  
  return (
    <Box
      onClick={!disabled && onClick ? onClick : undefined}
      sx={{
        width: size,
        height: size,
        borderRadius: 1,
        backgroundColor: placeholder.bgColor,
        border: `1px solid ${placeholder.color}20`,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: !disabled && onClick ? 'pointer' : 'default',
        transition: 'all 0.2s ease',
        '&:hover': !disabled && onClick ? {
          backgroundColor: placeholder.color + '10',
          transform: 'scale(1.05)',
        } : {},
      }}
    >
      {/* Icon */}
      <Typography
        sx={{
          fontSize: size > 32 ? '16px' : '12px',
          lineHeight: 1,
          mb: size > 32 ? 0.5 : 0,
        }}
      >
        {placeholder.icon}
      </Typography>
      
      {/* Extension label for larger thumbnails */}
      {size > 32 && (
        <Typography
          variant="caption"
          sx={{
            fontSize: '8px',
            fontWeight: 600,
            color: placeholder.color,
            textTransform: 'uppercase',
            lineHeight: 1,
          }}
        >
          {extension}
        </Typography>
      )}
    </Box>
  );
};

export default PlaceholderThumbnail;
