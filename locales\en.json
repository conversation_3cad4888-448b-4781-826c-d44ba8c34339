{"shared": {"edit": "Edit", "save": "Save", "soon": "Soon", "learnMore": "Learn more.", "cancel": "Cancel", "confirm": "Confirm", "item": "item", "items": "items", "item_capitalize": "<PERSON><PERSON>", "items_capitalize": "Items", "item_one": "{{count}} item", "item_few": "{{count}} items", "item_other": "{{count}} items", "none": "None", "create": "Create", "set": "Set", "minutes": "minutes", "done": "Done", "in": "in", "details": "Details", "noContentAvailable": "No content available", "change": "Change", "cash": "Cash", "nonCash": "Non Cash", "continue": "Continue", "delete": "Delete", "generate": "Generate", "category": "category", "category_capitalize": "Category", "categories": "categories", "categories_capitalize": "Categories", "symbol": "Symbol", "description": "Description", "columns": "Columns", "groupBy": "Group by", "active": "Active", "location": "Location", "location_few": "Locations", "code": "Code", "copy": "Copy", "loading": "Loading...", "search": "Search", "name": "Name", "netSales": "Net Sales", "price": "Price", "tva": "VAT", "payments": "Payments", "enterValue": "Enter value", "totalValue": "Total Value"}, "dashboard": {"noData": "No data available", "headerSearchClose": "Close", "headerSearchPrompt": "Try searching for terms like category names, products, or transactions.", "headerSearchResults": "Search results for:", "headerSearchPlaceholder": "Search", "notificationCenterTitle": "Notifications", "upToDate": "You're up to date!", "welcome": "Welcome back", "sellpoint": "Select a Sellpoint", "date": "Date", "today": "Today", "yesterday": "Yesterday", "thisWeek": "This Week", "lastWeek": "Last Week", "thisMonth": "This Month", "lastMonth": "Last Month", "thisYear": "This Year", "lastYear": "Last Year", "balance": "Go to balance", "goToTransations": "Go to Transactions", "viewReports": "View Reports", "viewPermissions": "View Permissions", "payment": "Take a payment", "editMenu": "Edit a Menu", "item": "Add an item", "setupGuide": "Setup Guide", "youAre": "You're", "setUp": "set up.", "grossSales": "Gross sales", "grossSales2": "Gross sales", "bills": "Closed orders", "averageSale": "Average per Sale", "covers": "Covers", "teamMembers": "Team members", "openSales": "Open sales pending", "openBills": "Open Bills", "customers": "Customers", "paymentTypes": "Top Payment Types", "byGross": "by gross sales", "categories": "Top Categories", "totalCustomers": "Total customers", "newCustomers": "New customers", "returningCustomers": "Returning customers", "averageVisits": "Average visits per customer", "topItemsSales": "Top items", "topCategories": "Top Categories", "topModifiers": "Top modifiers", "sold": "Sold", "sales": "Total Collected", "byWeightInSales": "by weight in sales", "topPopularCategories": "Top Popular Categories", "recomendedProducts": "Recommended Products", "selioRetail": "<PERSON><PERSON> for Retail", "selioRestaurants": "Selio for Restaurants", "giftCards": "Gift Cards", "paymentLinks": "Payment Links", "loyalty": "Loyalty", "payroll": "Payroll", "vsPrior": "vs. prior period", "notEnoughData": "Not enough data to compare", "noOpenOrders": "No open orders", "orderNote": "Order Note:", "switchAccount": "Switch Account"}, "itemsLibrary": {"title": "Items Library", "desc": "Centralized catalog of all your items, including details like pricing, categories, and tax settings. Easily manage and update your offerings. ", "items-name": "Item Name", "items-unit": "Unit", "items-business": "Business Type", "items-date": "Latest Update", "editItem": "<PERSON>em", "nameAndDescriptionVariations": "Name and Description Variations", "publicName": "Public Name", "publicDescription": "Public Description", "ingredients": "Ingredients", "missingCategory": "Missing Category"}, "categoryLibrary": {"title": "Category Library", "desc": "Organize products into logical categories such as Pizza, Pasta, or Desserts. These are essential for routing orders to prep stations and structuring reports. ", "createCategory": "Create Category", "editCategory": "Edit Category", "noCategoriesYet": "No categories yet", "noCategoriesYetDescription": "Create a category to start"}, "posMenus": {"title": "Menus", "desc": "Create and manage the menus available on your POS devices, including items, categories, and availability settings. "}, "profile": {"accountSettings": "Account <PERSON><PERSON>", "cookiePolicy": "Cookie policy", "cookiePreferences": "Cookie preferences", "termsOfService": "Terms of service", "orderSelio": "Order Selio hardware", "signOut": "Sign out"}, "comingSoon": {"title": "Coming Soon", "goBack": "Go Back", "home": "Home"}, "permissions": {"filterPermissions": "Filter permissions", "title": "Team Permissions", "description": "Team Permissions allow you to set up restrictions for team members using your account. These permissions apply to all access points.", "pages": {"orders": {"title": "Orders", "description": "Grant access to view, edit, and cancel all open orders."}, "customers": {"title": "Customers", "description": "Grant access to create, view, edit, and delete customer data."}, "giftCards": {"title": "Gift Cards", "description": "Grant access to sell and top up gift cards."}, "settings": {"title": "Settings", "description": "Grant access to view and edit POS device settings."}}, "groupTitles": {"orders": "Orders", "payments": "Payments", "actions": "Actions", "discountsAndTips": "Discounts and Tips", "rewards": "Rewards"}, "owner": {"title": "Owner", "description": "Owner has full control and can perform any action."}, "super": {"title": "Super", "description": "User with permissions similar to the Owner, but without a few exclusive permissions."}, "accessBillOwnedByOther": {"title": "Access Bill Owned By Other", "description": "Allows access to bills owned by another owner."}, "sellItems": {"title": "<PERSON><PERSON>", "description": "Permission to add items to an order via menu selection, swipe, or product code scanning."}, "takePayments": {"title": "Take Payments", "description": "Permission to process payments when clicking the Pay button on an order."}, "moveItems": {"title": "Move Items", "description": "Permission to move items, considering the 'Access Bill Owned By Other' right when selecting sources and destinations."}, "assignBill": {"title": "Assign Bill", "description": "Permission to assign a bill, taking 'Access Bill Owned By Other' into account when selecting the source."}, "voidItems": {"title": "Void Items", "description": "Permission to void items, considering the 'Access Bill Owned By Other' right."}, "compItems": {"title": "Comp Items", "description": "Permission to comp items, considering the 'Access Bill Owned By Other' right."}, "printBill": {"title": "Print Bill", "description": "Permission to print the bill when clicking the Print Bill button."}, "applyDiscount": {"title": "Apply Discount", "description": "Permission to apply discounts when clicking the Discounts button in actions."}, "searchCustomer": {"title": "Search Customer", "description": "If not permitted, a QR code scanner appears when clicking the Customers button."}, "editCustomerNote": {"title": "Edit Customer Note", "description": "Permission to edit notes on a customer."}, "addCustomerDetails": {"title": "Add Customer Details", "description": "Permission to add company and address details for a customer."}, "viewCustomerPersonalInfo": {"title": "View Customer Personal Info", "description": "Permission to view customer's personal information."}, "removeCustomerFromBill": {"title": "Remove Customer From Bill", "description": "Permission to remove a customer from a sale when clicking the Remove From Sale button."}, "createCustomer": {"title": "Create Customer", "description": "Permission to create a customer, implicitly requires Search Customer permission."}, "redeemCustomerRewards": {"title": "Redeem Customer <PERSON><PERSON>s", "description": "If not permitted, a QR code scanner appears when clicking the rewards/coupons button."}, "applyTips": {"title": "Apply Tips", "description": "Permission to apply tips when clicking the Tips button."}, "sellPhysicalGiftCard": {"title": "<PERSON>ll Physical Gift Card", "description": "Permission to sell a physical gift card."}, "sellEGiftCard": {"title": "Sell eGift Card", "description": "Permission to sell an eGift card."}, "topUpGiftCard": {"title": "Top Up Gift Card", "description": "Permission to add value to a gift card after checking the balance."}, "changeItemAvailability": {"title": "Change Item Availability", "description": "Permission to change item availability via long press on a product."}, "viewClosedBillOwnedByOther": {"title": "View Closed Bill Owned By Other", "description": "Permission to view closed bills of others in Transactions, Transfers, and Sales Reports."}, "runCloseOfDay": {"title": "Run Close of Day", "description": "Permission to run the end-of-day process."}, "accessFiscalOperations": {"title": "Access Fiscal Operations", "description": "Permission to access fiscal operations from the Fiscal Register."}, "manageHardware": {"title": "Manage Hardware", "description": "Permission to manage hardware settings in the account's Settings."}, "noPermissionSetsYet": "No permission sets yet", "noPermissionSetsYetDescription": "Create a permission set to start", "createPermissionSet": "Create permission set", "access": "Access"}, "devices": {"posDevices": {"title": "Point of Sale devices", "description": "To manage permissions for anyone accessing Point of Sale without ateam member login, edit ", "teamPermissions": "Team Permissions."}, "menuBehaviour": {"title": "<PERSON><PERSON>", "description": "Customize order entry flows and menu navigation behavior on Restaurants POS.", "stepType": "Step Type", "returnToHomeScreen": "Return to Home Screen", "returnToHomeScreenDescription": "After adding an item, you will return to the home screen of the menu.", "stayInPlace": "Stay in Place", "stayInPlaceDescription": "After adding an item, you will return to the last screen you viewed before adding the item."}, "tips": {"title": "Tips", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras bibendum semper erat, eget tincidunt purus. Integer et vehicula ipsum. Donec eget nunc sed mi accumsan aliquam nec ornare mi."}, "discounts": {"title": "Discounts", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras bibendum semper erat, eget tincidunt purus. Integer et vehicula ipsum. Donec eget nunc sed mi accumsan aliquam nec ornare mi."}, "courses": {"title": "Courses", "description": "Courses streamline your kitchen workflow, ensuring that dishes are prepared and delivered to your guests at the perfect moment. For detailed instructions, please click ", "categoriesAssigned": "categories assigned", "straightAway": "Straight away", "name": "Name", "addCourse": "Add course", "tableCaption": "The order in which you arrange your courses will directly impact theirdisplay on both the Restaurant POS interface and printed tickets.", "editStraightFireCategories": "Edit Straight Fire categories", "editStraightFireCategoriesDescription": "Items marked as 'Straight Fire' bypass coursing and are sent directly to the kitchen. This feature is typically used for drinks or other items that don't require coordination with specific courses.", "assignedCategories": "Assigned categories", "assignedCategoriesDescription": "Select which categories of items will be automatically added to a straight fire group on the POS."}}, "sellpoints": {"daily": "Daily", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "mondayShort": "M", "tuesdayShort": "T", "wednesdayShort": "W", "thursdayShort": "T", "fridayShort": "F", "saturdayShort": "S", "sundayShort": "S"}, "menu": {"customers": "Customers", "directory": "Directory", "feedback": "<PERSON><PERSON><PERSON>", "insights": "Insights", "giftCards": "Gift Cards", "extraCharges": "Extra Charges", "overview": "Overview", "eGiftCards": "eGift Cards", "plasticGiftCards": "Plastic Gift Cards", "houseAccounts": "House Accounts", "loyalty": "Loyalty", "reports": "Reports", "visits": "Visits", "sales": "Sales", "topCustomers": "Top Customers", "activity": "Activity", "allActivity": "All Activity", "suspiciousActivity": "Suspicious Activity", "marketing": "Marketing", "loyaltyPromotions": "Loyalty Promotions", "itemsAndOrders": "Items & Orders", "menus": "Menus", "Modifiers": "Modifiers", "itemLibrary": "Items Library", "categories": "Items Category", "prepStations": "Prep Stations", "prepStationsDescription": "Assign categories from your category library to specific prep stations (e.g., Kitchen – assign Pizza, Pasta, Salads, and Desserts) to optimize workflow and order routing.", "modifiers": "Modifiers", "modifiersDescription": "Customize items with add-ons, options, or variations to give customers more choices and streamline kitchen instructions. ", "compAndVoid": "Comp and Void", "units": "Measure Units", "orders": "Orders", "orderPartners": "Partners Order", "payments": "Payments", "invoices": "Invoices", "virtualTerminal": "Virtual Terminal", "reporting": "Reporting", "transactions": "Transactions", "transactionsDescription": "A transaction only records completed payments related to an order – regardless of whether the payment was made in cash, by card, with a voucher, or through third-party apps.", "salesRevenue": "Sales Revenue", "salesTrends": "Sales Trends", "paymentMethods": "Payment Methods", "paymentMethodsDescription": "Your payment options that you provide, the smoother and more enjoyable the experience will be for your customers.", "itemSales": "Item Sales", "categorySales": "Category Sales", "prepStationSales": "Prep Station Sales", "sectionSales": "Section Sales", "teamSales": "Team Sales", "kitchenPerformance": "Kitchen Performance", "modifierSales": "Modifier Sales", "modifierSalesDescription": "Sales performance by modifiers, including prep station and net sales after discounts.", "comps": " Gratuities (Comps)", "voids": "Cancellations (Voids)", "taxes": "Taxes", "vat": "VAT Collected", "serviceCharges": "Service Charges", "activityLog": "Activity Log", "staff": "Staff", "shifts": "Shifts", "teamMembers": "Team Members", "teamMembersWorkingArea": "Working Area Assignments", "roles": "Roles", "permissions": "Permissions", "settings": "Settings", "accountAndSettings": "Account & settings", "personalInformation": "Personal Information", "signInAndSecurity": "Sign in & Security", "preferences": "Preferences", "businessInformation": "Business Information", "aboutMyBusiness": "About My Business", "emailNotifications": "Email Notifications", "outageNotifications": "Outage Notifications", "sellpoints": "Sellpoints", "pricingAndSubscriptions": "Pricing & Subscriptions", "bankAccounts": "Bank Accounts", "transfers": "Transfers", "receipts": "Receipts", "salesTaxes": "Sales Taxes", "serviceChanges": "Service Changes", "fulfillment": "Fulfilment", "selioHardware": "Selio Hardware", "informationRequests": "Information Requests", "devicesManagement": "Devices Management", "pointOfSales": "Point of Sales", "posDevices": "Devices", "floorPlans": "Floor Plans", "sections": "Sections", "allocations": "Allocations", "serviceSettings": "Service Settings", "menuBehaviour": "<PERSON><PERSON>", "discounts": "Discounts", "courses": "Courses", "kitchenDisplays": "Kitchen Displays", "kitchenSettings": "Kitchen Settings", "appIntegrations": "Partner Integrations", "quickAccess": "Quick access", "allProducts": "All products", "home": "Home", "editQuickAccess": "Edit quick access", "devices": "Devices", "tips": "Tips", "coupons": "Coupons", "promotions": "Promotions", "deleteConfirmation": "Are you sure you want to delete this menu?", "delete?": "Delete", "createMenu": "Create a Menu", "createMenuStep1": "Choose the best way to build a menu for your needs", "uploadMenu": "Upload a file, photo, or URL of your existing menu", "uploadMenuDescription": "We'll automatically convert it into your Selio Menu", "importMenu": "Import a 3rd party menu", "importMenuDescription": "Import an existing menu from one of our suported third parties.", "buildManually": "Build it manually", "comingSoon": "Coming soon", "editPOSLayout": "POS Layout", "addMenuGroup": "Add Menu Group", "emptyMenu": "Your menu is empty", "emptyMenuDescription": "Create a Menu Group to get started!", "emptyMenuGroup": "Your menu group is empty", "emptyMenuGroupDescription": "Create an item to get started!", "stuck": "Feeling stuck? Let us help you!", "stuckDescription": "Learn about menus through our video content, or save time and let us build your menu.", "learnAboutMenus": "Learn About Menus", "letUsBuildYourMenu": "Let us build your menu", "requiredField": "* is a required field", "noMenusYet": "No menus yet", "noMenusYetDescription": "Create menus to organize and showcase your food and drinks across all locations. Seamlessly manage what appears on your Point of Sale.", "menuList": "Menu List", "tablet": "Tablet", "phone": "Phone", "page": "Page", "addPage": "Add page", "removePage": "Remove page", "orientation": "View", "posHomeScreen": "POS Home Screen", "noItems": "No items", "addItem": "Add Item", "editItem": "<PERSON>em", "removeItem": "Remove Item", "addGroupInside": "Add Group inside ", "editGroup": "Edit Group ", "removeGroup": "Remove Group", "details": "Details", "tvaTaxes": "VAT Taxes", "kitchenName": "Kitchen Name", "kitchenNameTooltip": "Kitchen names let you customise how your items appear on printed order tickets and kitchen displays", "ageRestriction": "Age Restriction", "ageRestrictionTooltip": "When an age-restricted item is added, the checkout will prompt for ID scan or date of birth entry. Learn more", "ageVerification": "Age verification", "ageVerificationTooltip": "When an age-restricted item is added, the checkout will prompt for ID scan or date of birth entry. ", "allLocations": "All locations", "noRestriction": "No restriction", "ageRestriction18": "18+ (alcohol, tobacco, CBD, adult content)", "customAgeRestriction": "Custom age restriction", "ageRestrictionTooltip2": "Age restrictions help you stay compliant with local laws. <PERSON><PERSON> is not liable for sales of restricted items to minors.", "orBrowseImageLibrary": "or browse image library", "dragAndDrop": "Drag and drop images here", "image": "Image", "nutritionalInformation": "Nutritional information", "calorieCount": "Calorie count", "calories": "calories", "dietaryPreferences": "Dietary preferences", "allergens": "Allergens", "nutritionalValues": "Nutritional values", "modifiersTooltip": "Let customers customize items with toppings, extras, or special requests like 'extra cheese'. Learn more", "modifiersTooltipUnavailable": "Modifiers not available.", "forceModify": "Force Modify", "forceModifyTooltip": "Enable staff to select a modifier before adding the item to the order.", "forceModifyTooltip2": "Use it when options (like size, doneness, or extras) are essential and should never be skipped.", "forceQuantity": "Force Quantity (pop-up)", "forceQuantityTooltip": "Prompts staff to enter a quantity every time the item is added to the order.", "forceQuantityTooltip2": "Ideal for items sold in variable amounts — like grams, pieces, or liters.", "ordering": "Ordering", "newFeature": "New Feature", "itemPrepTime": "Item Prep Time", "menuGroupColor": "Menu Group Color", "itemPrepTimeDescription": "Set how long it takes to prepare this item. This helps improve kitchen display timing and gives customers more accurate serving at the table, pickup or delivery estimates.", "itemPrepTimeAlert": "<PERSON><PERSON> will use for orders the longest prep time among items in the cart to calculate when the order will be ready.", "useDefault": "Use default", "availableImmediately": "Available immediately", "customItemPrepTime": "Custom item prep time", "ordersWithThisItemWillBeReady": "Orders with this item will be ready", "in30Minutes": "in 30 minutes", "immediately": "immediately", "whereItIsAvailable": "where it's available.", "addComboProduct": "Add Combo Product", "addFunction": "Add Function", "addGroupIn": "New Menu Group", "goToFirstPage": "Go to first page", "editMenu": "<PERSON>", "deleteMenu": "Delete Menu", "changeTo": "Change to", "removeTile": "Remove tile", "deleting": "Deleting...", "menuDeleted": "<PERSON><PERSON> deleted", "menuGroup": "Menu Group", "itemName": "Item Name", "totalSales": "Total Sales", "currentPage": "Current page", "moveToPage": "Move to page", "pageFullError": "Selected page is currently full. Please select a different one.", "cannotChangeColor": "Cannot change color", "nestedGroupsInfo": "Contains Menu Groups. View full structure in POS Layout.", "menuName": "<PERSON>u Name", "backToMobileSite": "Back to mobile site", "useDesktopSiteInstead": "Use desktop site instead", "addFunctions": "Add Functions", "add": "Add", "function": "function", "function_one": "{{count}} function", "function_few": "{{count}} functions", "function_other": "{{count}} functions", "functions": "functions", "newComboProduct": "New Combo Product", "editComboProduct": "Edit Combo Product", "displayName": "Display Name", "color": "Color", "steps": "Steps", "stepNameMessage": "Step Name / Message", "noOfItemsSelected": "No. of Items Selected", "stepType": "Step Type", "required": "Required", "optional": "Optional", "requiredDescription": "After completing a sale you will return to the floor plans or list of bills", "optionalDescription": "After completing a sale you will return to a New Sale and see your grid of items", "addNewStep": "Add new step", "addItemsOrEntireMenuGroup": "Add Items or Entire Menu Group", "addItems": "Add Items", "addMenuGroup2": "Add Menu Group", "teamRevenue": "Team Performance", "teamRevenueDescription1": "Evaluates each team member’s daily work, including all collected payments and rendered sales (including granted comps).", "teamRevenueDescription2": "Automatically calculates the total and corresponding commission. Ideal for fair payouts and full transparency.", "combo": "combo", "items": "Items", "Item": "<PERSON><PERSON>", "Price": "Price", "menuType": "Menu Type", "excludedFromDiscount": "Excluded from Discount", "excludedFromDiscountTooltip": "Automatically protects key menu items (e.g. premium drinks, cigarettes) from any discount, whether manual or promotional.", "images": "Images", "dropFilesHere": "Drop files here", "dragAndDropOrClickToUploadFiles": "Drag & drop or click to upload files", "dragAndDropOrClickToUploadFile": "Drag & drop or click to upload file", "uploadUpTo3Images": "Upload up to 3 images (JPEG, PNG, WebP). Max 300kb per image.", "uploadUpTo1Image": "Upload up to 1 image (JPEG, PNG, WebP). Max 300kb per image.", "uploading": "Uploading...", "noFilesUploaded": "No files uploaded", "uploadedFile": "Uploaded file:", "invoicesDescription": "Access and manage all order invoices, including details like payment status, order source, and customer info."}, "items": {"compAndVoid": {"title": "Comp and Void", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras bibendum semper erat, eget tincidunt purus. Integer et vehicula ipsum. Donec eget nunc sed mi accumsan aliquam nec ornare mi.", "compReasons": "Comp Reasons", "createComp": "Create Comp Reason", "voidReasons": "Void reasons", "createVoid": "Create Void Reason"}}, "nutritionalValues": {"energy": "Energy (kJ/kcal)", "fat": "Fat (g)", "saturatedFat": "Saturated fatty acids (g)", "carbohydrates": "Carbohydrates (g)", "sugar": "Sugars (g)", "fiber": "Fiber (g)", "protein": "<PERSON><PERSON> (g)", "salt": "Salt (g)", "portion": "Portion", "per100g": "100 Grams"}, "add-item": {"dietary-preferences": {"dairy-free": {"title": "Dairy-free", "description": "Does not contain milk or milk-based ingredients"}, "gluten-free": {"title": "Gluten-free", "description": "Does not contain gluten or gluten-containing ingredients"}, "halal": {"title": "<PERSON><PERSON>", "description": "Prepared according to Islamic dietary laws"}, "kosher": {"title": "<PERSON><PERSON>", "description": "Prepared according to Jewish dietary laws"}, "nut-free": {"title": "Nut-free", "description": "Free from tree nuts and peanuts"}, "vegan": {"title": "Vegan", "description": "No animal products, including eggs, dairy, and honey"}, "vegetarian": {"title": "Vegetarian", "description": "No meat, but may contain eggs or dairy"}, "low-sugar": {"title": "Low sugar", "description": "Example: under 5g of sugar per 100g"}, "low-carb": {"title": "Low carb", "description": "Suitable for keto or low-carb diets"}, "low-sodium": {"title": "Low sodium", "description": "Prepared without added salt"}}, "allergens": {"celery": {"title": "Celery", "description": "Includes stalks, leaves, seeds, and root"}, "crustaceans": {"title": "Crustaceans", "description": "e.g. crab, lobster, shrimp"}, "eggs": {"title": "Eggs", "description": "From all bird species"}, "fish": {"title": "Fish", "description": "Includes all species of fish and fish products"}, "gluten": {"title": "Gluten", "description": "From wheat, barley, rye, oats, spelt"}, "lupin": {"title": "<PERSON><PERSON>", "description": "Flour or seeds from the lupin plant"}, "milk": {"title": "Milk", "description": "Including lactose and dairy products"}, "molluscs": {"title": "Molluscs", "description": "e.g. mussels, squid, oysters, snails"}, "mustard": {"title": "Mustard", "description": "Includes seeds, powder, and oil"}, "peanuts": {"title": "Peanuts", "description": "Groundnuts and all peanut products"}, "sesame": {"title": "Sesame", "description": "Seeds, paste, oil, etc."}, "soy": {"title": "Soy", "description": "Soybeans and products derived from soy"}, "sulphites": {"title": "Sulphites", "description": "Sulfur dioxide, >10mg/kg or 10mg/l"}, "tree-nuts": {"title": "Tree nuts", "description": "e.g. almonds, hazelnuts, walnuts, cashews, pistachios"}}}, "measure-units": {"title": "Measure Units", "description": "Measurement units (e.g., pieces, grams, liters) are already defined to ensure accurate inventory tracking and pricing.", "table-name": "Name", "table-code": "Code", "table-desc": "Description", "piece": {"unit-name": "Piece", "unit-code": "pc", "unit-desc": "For individual items like burger, bottle of water, muffin, spring roll."}, "kilogram": {"unit-name": "Kilogram", "unit-code": "kg", "unit-desc": "For bulk prep or raw items like meat for shawarma, flour for pizza dough."}, "gram": {"unit-name": "Gram", "unit-code": "g", "unit-desc": "For precise items like parmesan topping, chili flakes, truffle paste."}, "liter": {"unit-name": "Liter", "unit-code": "L", "unit-desc": "For liquids like lemonade, soup stock, house wine (served by carafe)."}, "portion": {"unit-name": "Portion", "unit-code": "por", "unit-desc": "For served dishes like 1 portion of pasta, 1 portion of tiramisu."}}, "support": {"support-link": "Learn more in Support Center."}, "preferences": {"title": "Preferences", "description": "Customize your personal experience, including language and cookies preferences across all devices. ", "manageCookies": "Manage Cookies", "cookiesDescription": "Selio uses cookies (and similar technologies) for many purposes, including maintaining security, improving and customizing our services, and enabling relevant marketing. Please review our use of cookies to accept or reject non-essential cookies.", "customizeCookiePreferences": "Customize cookie preferences", "language": "Language", "english": "English", "romanian": "Romanian"}, "reports": {"salesRevenue": "Sales Revenue", "salesRevenueDescription": "Sales revenue is income received from sales of goods or services.", "learnMoreInSupportCenter": "Learn more in Support Center.", "extraCharges": "Extra Charges", "extraChargesDescription": "Summary of additional charges applied, including fees, quantities, and net sales.", "emmitedBy": "emmited by", "fiscalReceipt": "fiscal receipt ", "completeInvoices": "Complete Invoices", "voidedInvoices": "Voided Invoices", "compedInvoices": "Comped Invoices", "totalCollected": "Total Collected", "completeInvoicesInfo": "The total number of completed invoices.", "voidedInvoicesInfo": "The total number of voided invoices.", "compedInvoicesInfo": "The total number of comped invoices.", "totalCollectedInfo": "The total amount collected from completed invoices.", "searchInvoices": "Search all invoices"}, "transactionsPage": {"completeTransactions": "Complete Transactions", "voidedTransactions": "Cancelled (Voided) Transactions", "compedTransactions": "Gratuities (Comped) Transactions", "totalCollected": "Total Collected", "completeTransactionsInfo": "The total number of completed transactions. A transaction will not appear until fully paid out.", "voidedTransactionsInfo": "Total number of cancelled transactions", "compedTransactionsInfo": "Total number of gratuities transactions", "totalCollectedInfo": "Total amount collected from completed transactions", "netSalesInfo": "Total amount collected from completed transactions minus cancelled transactions", "searchTransactions": "Search all transactions by payment, name, or value", "ownedBy": "owned by ", "fiscalReceipt": "fiscal receipt", "sendReceipt": "Send Receipt", "sendReceiptByEmail": "Send Receipt by Email", "enterValidEmailAddress": "Enter a valid email address", "send": "Send", "sale": "Sale", "voidedSale": "Voided Sale", "comped": "Comped", "order": "Order", "openedAt": "Opened at", "closedBy": "Closed by", "closedFrom": "Closed from", "source": "Source", "saleAttributedTo": "Sale attributed to", "covers": "Covers", "forHere": "For Here", "toGo": "To Go", "billsIssued": "Bills Issued", "issuedBy": "Issued by", "Bill": "Bill", "Receipt": "Receipt", "closedWith": "Closed with", "hasDiscounts": "Has Discounts", "hasTransfers": "Has Transfers", "hasBillsIssued": "Has Bills Issued", "dayOfWeek": "DAY OF WEEK", "timeOfDay": "TIME OF DAY", "allDay": "All day", "salesPie": "Think of your sales like a pie. ", "salesPie1": " is the whole pie – everything you earned.", "salesPie2": "is what's left after taking out slices for discounts, Coupons and other Promotions (Combo and Promotional Offers, etc).", "salesPie3": "is the pie that's left! It's the actual money you keep.", "totalSales": "Total sales", "seePaymentMethods": "See Payment Methods", "reason": "Reason"}, "weekDays": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "mon": "M", "tue": "T", "wed": "W", "thu": "T", "fri": "F", "sat": "S", "sun": "S"}, "reportFilters": {"custom": "Custom", "untilNextDay": "Until next day", "allMembers": "All members", "allFloors": "All floors", "allLocations": "All locations", "allServiceTypes": "All Service Types", "allSources": "All Sources", "allDay": "All day", "For Here": "For Here", "To Go": "To Go", "Here": "Here", "start": "Start", "end": "End"}, "reportsPage": {"itemSalesDescription": "Detailed sales report by individual items, including prep station, category, and net sales after discounts.", "totalCollected": "Total collected on", "were": "were", "thisWas": "This was", "fromTheSameDayOfThePreviousWeek": "from the same day of the previous week.", "paymentsFrom": "payments from", "allMembers": "All members", "allFloors": "All floors", "allServiceTypes": "All Service Types", "allSources": "All Sources", "paymentMethod": "Payment Method", "details": "Details", "extraDetails": "Extra Details", "count": "Count", "value": "Value", "prepStation": "Prep Station", "unit": "Unit", "all": "All", "salesFrom": "sales from", "thisItemWasSoldIn": "This item was sold in", "combination": "unique combination", "combinations": "unique combinations", "itemName": "Item Name", "itemsSold": "Items Sold", "grossSales": "Gross Sales", "discounts": "Discounts", "coupons": "Coupons", "noData": "No Data", "modifierName": "Modifier Name", "quantity": "Quantity", "itemsDiscountApplied": "Items Discount Applied", "giftCardCouponsApplied": "Gift Card Coupons Applied", "modifiersDiscountApplied": "Modifiers Discount Applied", "promotionsDescription": "Overview of active promotions applied to items, gift cards, or extra charges.", "itemsPromotionsApplied": "Items Promotions Applied", "giftCardPromotionsApplied": "Gift Card Promotions Applied", "extraChargesPromotionsApplied": "Extra Charges Promotions Applied", "totalAmount": "Items Promotions Amount", "reason": "Reason", "itemsComped": "Items Comped", "grossComps": "Gross Comps", "promotions": "Promotions", "netComps": "Net Comps", "compedFrom": "comped from", "thisItemWasCompedIn": "This item was comped in", "modifier": "Modifier", "type": "Type", "tipsAmount": "<PERSON><PERSON><PERSON> Amount", "totalCollected2": "Total Collected", "extraChargesSales": "Extra Charges Sales", "giftCardsSales": "Gift Cards Sales", "itemsSales": "Items Sales", "modifiersSales": "Modifiers Sales", "addFilter": "Add Filter"}, "paymentMethods": {"cash": "Cash", "card": "Card", "giftCard": "Gift Card", "voucher": "Voucher", "credit": "Credit", "other": "Other", "mealTicket": "Meal Ticket", "online": "Online", "valueTicket": "Value Ticket", "wireTransfer": "Wire Transfer", "cashless": "Cashless", "3rdParty": "3rd Party", "paymentMethod": "Payment Method", "paymentMethod2": "Payment Method", "count": "Count", "collected": "Collected"}, "categorySales": {"title": "Category Sales", "description": "Breakdown of sales, modifiers, and discounts by category to monitor performance and profitability. ", "itemsSold": "Items Sold", "itemsSales": "Items Sales", "modifiersSold": "Modifiers Sold", "modifiersSales": "Modifiers Sales", "grossSales": "Gross Sales", "grossSales2": "Categories: Gross Sales", "discounts": "Discounts", "coupons": "Coupons", "promotions": "Promotions"}, "itemSales": {"topGrossSales": "Items: Gross Sales", "unit": "Unit", "itemsSold": "Items Sold", "grossSales": "Gross Sales", "discounts": "Discounts", "coupons": "Coupons", "prepStation": "Prep Station", "promotions": "Promotions"}, "modifierSales": {"topGrossSales": "Modifiers: Gross Sales", "modifier": "Modifier", "unit": "Unit", "modifiersSold": "Modifiers Sold", "modifiersSales": "Modifiers Sales"}, "giftCards": {"title": "Gift Cards", "description": "Sales summary of physical and digital gift cards, including quantities sold and net revenue. ", "Physical Sold": "Physical Sold", "Digital Sold": "Digital Sold", "Gross Sales": "Gross Sales", "Net Sales": "Net Sales", "type": "Type", "quantity": "Quantity", "Total Comped": "Total Comped", "Comped Items": "Comped Items", "Comped Modifiers": "Comped Modifiers", "Comped Extra Charges": "Comped Extra Charges", "Comped Tips": "Comped Tips", "Total Sold": "Total Sold", "Regular": "Regular", "Combos": "Combos", "w/ Discounts": "Discounts", "w/ Promotions": "Promotions", "Taxable Sales": "Taxable Sales", "Non Taxable Sales": "Non Taxable Sales", "VAT Amount": "VAT Amount"}, "extraCharges": {"description": "Summary of additional charges applied, including fees, quantities, and net sales."}, "discounts": {"title": "Discounts", "description": "Overview of discounts applied to items, modifiers, and gift cards with total impact on sales.", "topDiscounts": "Discounts: Amount Discounted", "totalValue": "Total Amount", "rewardCoupons": "<PERSON><PERSON> Coupons"}, "coupons": {"title": "Coupons", "description": "Summary of coupon usage across items, modifiers, and gift cards with total discount amounts. "}, "tips": {"title": "Tips", "description": "Report of cash and non-cash tips collected. ", "teamMember": "Team Member", "nonCashValue": "Non Cash Value", "cashValue": "Cash Value", "totalAmount": "Total Amount", "noTipsYet": "No tips yet", "noTipsYetDescription": "Create a tip to get started.", "createTip": "Create tip", "percentage": "Percentage"}, "comps": {"description": "Summary of all comped sales and their impact on revenue. "}, "voids": {"title": "Cancellations (Voids)", "description": "Detailed report of voided items and modifiers, including reasons and total amounts voided. ", "topVoids": "Voided Items: Value", "reason": "Reason", "itemsVoided": "Items Voided", "amountVoided": "Amount Voided"}, "vat": {"description": "Summary of sales by VAT rates, including gross sales, discounts, and VAT amounts. ", "vatAmount": "VAT Amount"}, "members": {"title": "Team Members", "description": "Manage your staff accounts, roles, and contact details. Easily add or remove team members and control their access. ", "learnMoreInSupportCenter": "Learn more in Support Center.", "addTeamMember": "Add a Team Member", "filterTeamMembers": "Filter Team Members", "displayName": "Display Name", "role": "Role", "personalInformation": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "phone": "Phone", "email": "Email", "passcode": "Passcode", "emailDescription": "Adding an email address will invite the team member to access their account in the Selio App.", "permissions": "Permissions set", "editTeamMember": "Edit team member", "credentials": "Credentials", "credentialsDescription": "These credentials allow this team member to use Se<PERSON> with the permissions set up above.", "personalPasscode": "Personal passcode", "personalPasscodeDescription": "This passcode is used for logging in and time tracking at any point of sale on a shared device.", "personalPasscodeDescription2": "This team member will be invited to create their own email login for accessing Dashboard and Point of Sale on their personal device.", "cashierWaiterBartender": "Cashier, Waiter, Bartender, etc", "missingRole": "Missing role"}, "createPermissions": {"title": "New permission set", "accessPoints": "Access points", "permissionSetName": "Permission set name", "customize": "Customize this set of permissions", "customizeSubtitle": "Customize the permissions below to tailor the way team members with this permission set use Selio.", "fullAccess": "Full access", "fullAccessDescription": "This enables all permissions except managing bank accounts. Only the account owner can manage bank accounts.", "searchPermissions": "Search permissions", "step2": {"title": "Choose access points", "subtitle": "Select the access points that team members with this permission set can use.", "all": "All", "allDescription": "All access points.", "sharedPointsOfSale": "Point of sale", "sharedPointsOfSaleDescription": "Gains access to use Selio POS app.", "dashboard": "Web", "dashboardDescription": "Gains access to use Selio Manager."}}, "signInSecurity": {"title": "Sign In & Security", "description": "Manage your login details and Selio POS passcode used for secure access and clock-ins. Keep your account safe with options like remote sign-out and verified contact information. ", "verified": "verified", "update": "Update", "add": "Add", "phone": "Phone", "password": "Password", "changePhone": "Change Phone Number", "addPhone": "Add Phone Number", "phoneDescription": "Enter a new phone number to associate with your account.", "phoneNumber": "Phone Number", "phoneTerms": "Message and data rates may apply. Message frequency may vary. See ", "personalPOSPasscode": "Personal POS Passcode for", "personalPOSPasscodeSubtitle": "Your personal POS passcode is used to log in and clock in on the Let's burger point of sale. Please don't share this passcode with anyone.", "signOutEverywhere": "Sign out everywhere", "signOutEverywhereSubtitle": "If you lost a device or left logged in a public computer, you can sign out everywhere except your current browser.", "privacyPolicy": "Privacy Policy", "termsAndConditions": "Terms of Service", "and": " and ", "changePassword": "Change password", "changePasswordDescription": "Enter your current password and a new password to associate with your account.", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "passwordsDoNotMatch": "Passwords do not match"}, "aboutMyBusiness": {"title": "About my business", "subtitle": "Manage your business account and settings.", "businessName": "Business Name", "businessOwner": "Business Owner"}, "emailNotifications": {"title": "Email notifications", "description": "Learn about new products or features and receive updates on how your business is performing. Automatically sent to your account email.", "accountEmails": "Account emails", "dailySalesSummary": "Daily sales summary", "monthlyAnnualSalesSummaries": "Monthly and annual sales summaries", "productUpdates": "Product updates", "programsEvents": "Programs and events", "newToSquare": "New to Selio", "governmentPrograms": "Government programs", "dailySalesSummaryDescription": "Receive emails about your sales at the end of each day.", "monthlyAnnualSalesSummariesDescription": "Receive email about your sales at the end of each month and year.", "productUpdatesDescription": "Be the first to know about new products, features, and exclusive beta testing opportunities. Tailor your updates to the products that matter most to you.", "programsEventsDescription": "Unlock special offers, event invitations, and valuable business tips from Selio.", "newToSquareDescription": "Learn how to set up your account and start using our powerful tools.", "governmentProgramsDescription": "Explore available european support to fuel your business growth.", "transactionalEmails": "Transactional emails", "perPayment": "Payment Confirmation", "perRefund": "Voided Sale Notification", "perTransfer": "Comped Sale Alert", "perPaymentDescription": "Get an email for each successful payment.", "perRefundDescription": "Receive an email whenever a sale is voided.", "perTransferDescription": "Get notified by email each time a comped sale is initiated."}, "outageNotifications": {"description": "Manage the email addresses and phone numbers that need to get outage notifications. You’ll only get outage notifications for the products you use.", "emailAddress": "Email address", "addPhoneNumber": "Add Phone Number", "changePhoneNumber": "Change Phone Number", "addPhoneNumberDescription": "By entering your phone number below, you agree to receive SMS outage notifications.", "addEmailAddress": "Add an email address", "updateEmailAddress": "Update email address", "addEmailAddressDescription": "Enter an email address you want to receive outage notifications.", "phoneNumber": "Phone number"}, "sellPointsPage": {"title": "Sellpoints", "description": "View and manage all your active sellpoints, including their status, linked devices, and assigned locations. Keep your operations organized and running smoothly. ", "address": "Address", "addSellpoint": "Add a Sellpoint", "basicInformation": "Basic Information", "basicInformationDescription": "Help customers recognize your transactions with your store’s location, and a brief description of your products or services.", "locationBusinessName": "Location Business Name", "whatsThis": "What's this?", "locationBusinessNameDescription": "Your Location Business Name can be edited up to 3 times every 12 months. Changing your Location Business Name does not count against your Business Name Change allowance. If you need to make a change outside of this window, please contact Support.", "locationBusinessNameDescription2": "Your location business name is what appears on your customers’ bank statements and the receipts you issue. This name is typically your Doing Business As (DBA) name, the one most familiar to your customers. This helps customers recognize your transactions, reducing confusion and chargebacks. Enhancing it with additional details like your store’s location or a brief description of your products or services can further aid customer recall.", "locationBusinessDetails": "Location details", "learnMore": "Learn more", "done": "Done", "businessDescription": "Business description", "businessAddress": "Business address", "locationType": "Location type", "addressLine1": "Address line 1", "addressLine2": "Address line 2", "city": "City", "county": "County", "zipCode": "ZIP code", "contactInformation": "Contact information", "emailAddress": "Email address", "phoneNumber": "Phone number", "socialContact": "Social contact", "branding": "Branding", "brandingDescription": "Your brand information applies to the look and feel of your receipts, invoices and marketing.", "dragAndDropLogo": "Drag and drop a logo or", "browseFiles": "browse files", "businessHours": "Business hours", "businessHoursDescription": "Let your clients know when you’re open.", "startAt": "Start at", "endAt": "End at", "nextDay": "Next day", "hideExceptions": "Hide exceptions", "addExceptions": "Add exceptions", "closed": "Closed", "bankInformation": "Bank information", "bankInformationDescription": "All payments for this location will be transferred into the account below.", "transferAccount": "Transfer account", "preferredLanguage": "Preffered language", "preferredLanguageDescription": "Set the language for Selio emails.", "selectLanguage": "Select language", "editSellpoint": "<PERSON>"}, "pricingSubscriptions": {"title": "Pricing & Subscriptions", "description": "Review your current plan, manage subscriptions, and explore available upgrades or add-ons to fit your business needs. ", "subscriptions": "Subscriptions", "youHave": "You have", "posDevice": "POS device", "started": "Started", "contactSelioSupport": "Contact Selio support to opt out of", "updateSubscription": "Update Subscription", "manageSubscription": "Manage Subscription", "youAreSubscribedTo": "You’re subscribed to Selio for Restaurants Free", "currentPlan": "Current Plan", "free": "Free", "includesBasic": "Includes basic Restaurant features for a single location.", "includesAllRestaurantFeatures": "Includes all Restaurant features with support for multiple locations and access for adding Selio KDS.", "startingAt": "starting at", "needMoreAdvancedFeatures": "Need more advanced features? ", "contactOurSales": "Contact our Sales", "unsubscribe": "Unsubscribe", "unsubscribeDescription": "You will still have access to paid features until the end of your billing cycle. ", "unsubscribe2": "Unsubscribe", "to": "to", "amount": "Amount", "invoiceNo": "Invoice No", "service": "Service", "date": "Date"}, "bankAccounts": {"title": "Bank Accounts", "description1": "Bank accounts must be verified before you can transfer money into or out of Selio.", "description2": "Verification usually takes a few minutes, but can take up to 4 business days for some banks.", "linkAccount": "Link an account", "unlinkAccount": "Unlink an account"}, "salesTaxes": {"title": "Sales Taxes", "description": "Easily set up and manage taxes for your business with Selio. Once configured, taxes are automatically applied to your POS orders.", "description2": "Make sure to review and file them on time—talk to your tax advisor if needed. ", "title2": "Sales Taxes", "description3": "Selio can help you create and manage taxes quickly and efficiently. It is your responsibility to review and file your taxes on time. Consult your tax advisor to ensure taxes are applied and filed correctly. Once you have set up your taxes from your Selio Online Dashboard or Selio App, taxes auto apply to your POS orders.", "taxName": "Tax Name", "status": "Status", "taxRate": "Tax Rate"}, "cookiesConsent": {"title": "Cookie preferences", "description": "When you interact with our mobile applications or online services, we obtain certain information by using automated technologies, such as cookies, web beacons, and other technologies described in our Cookie Policy. This information might be about you, your preferences, or your device. You may opt out of certain categories of cookies, other than those that are strictly necessary to provide you with our Services. Expand the different categories for more information and to make your choices. To effectuate your right to opt-out of the sharing of your personal information for purposes of targeted advertising, please toggle off \"retargeting or advertising technology\" cookies below.", "acceptAllBtn": "Accept all", "acceptNecessaryBtn": "Reject all", "savePreferencesBtn": "Save preferences", "closeIconLabel": "Close modal", "serviceCounterLabel": "Service|Services", "description2": "To effectuate your right to opt-out of the sharing of your personal information for purposes of targeted advertising, please toggle off \"retargeting or advertising technology\" cookies below.", "moreInformation": "More information", "privacyNotice": "Privacy Notice", "necessary": "Strictly Necessary Technology", "necessaryDescription": "These technologies are necessary for us to provide you with the Services.", "alwaysEnabled": "Always Enabled", "analytics": "Performance and analytical technology", "analyticsDescription": "This information is used to make sure our Services can cope with the volume of users, to help us correct errors in the Services and to measure use across our Services. These technologies help us understand if you have used our Services before so we can identify the number of unique users we receive. They also help us understand how long you spend using our Services and from where you have accessed the Services, so that we can improve the Services and learn about the most popular aspects of the Services.", "functionality": "Functionality technology", "functionalityDescription": "These technologies enable us to remember you have used our Services before, preferences you may have indicated, and information you have provided to us to give you a customized experience. For example, this would include ensuring the continuity of your registration process.", "retargeting": "Retargeting or advertising technology", "retargetingDescription": "We use third parties, for example, Google Analytics, to analyze statistical information from users of the Site. We might be able to associate such information with other information which we collect from you once we receive it from a third party. For example, these technologies may collect information about whether you clicked an ad to download our application."}, "devicesPage": {"title": "Devices Management", "description": "View and manage all your devices, including their status, linked sellpoints, and assigned locations. Keep your operations organized and running smoothly. ", "addDevice": "Add a Device", "basicInformation": "Basic Information", "basicInformationDescription": "Help customers recognize your transactions with your store’s location, and a brief description of your products or services.", "devicesAvailable": "devices Available", "devicesAvailableDescription": "Your subscription includes a total of 8 devices. Out of these, 3 are currently available to be added or paired. The rest are already connected to your account.", "of": "of", "newDevice": "New Device", "deviceDetails": "<PERSON>ce Det<PERSON>", "deviceName": "Device Name", "editDevice": "<PERSON>", "deviceType": "Device Type", "sellPoint": "Sell Point", "paired": "Paired", "unpaired": "Unpaired", "receipts": "Receipts", "receiptScreen": "Show receipt screen"}, "floorPlansPage": {"title": "Floor Plans", "description": "Design and manage your restaurant’s table layout to match your real-life setup. Improve order tracking and service by assigning orders to specific tables. ", "description2": "Improve order tracking and service by assigning orders to specific tables. ", "createFloorPlan": "Create a Floor Plan", "addNewSection": "Add New Section", "prefix": "Prefix", "numberOfTables": "Number of Tables", "tableSize": "Table Size", "tableShape": "Table Shape", "tableShapeDescription": "Choose the shape of the table.", "editFloorPlanFor": "Edit Floor Plan for ", "addTable": "Add a Table", "numberRequired": "Number is required", "tagRequired": "Tag is required", "tableAlreadyExists": "Cannot add table with a number that already exists", "tagAlreadyExists": "Cannot add table with a tag that already exists", "addNewTable": "Add New Table", "number": "Number", "shape": "<PERSON><PERSON><PERSON>", "active": "Active", "tables": "Tables", "noFloorPlansYet": "No floor plans yet", "noFloorPlansYetDescription": "Create a floor plan to get started", "editFloorPlan": "Edit Floor Plan", "deleteFloorPlan": "Delete Floor Plan"}, "tipsPage": {"title": "Tips", "description": "Track and manage tips procentage through your account. ", "createNewTip": "Create new Tip", "active": "Active", "tables": "Tables", "name": "Name", "percentage": "Percentage", "editTipPercentage": "Edit Tip Percentage"}, "discountsPage": {"title": "Discounts", "description": "Create and manage custom discounts for your items or orders. ", "description2": "Apply them easily during checkout to boost promotions and customer satisfaction. ", "createDiscount": "Create discount", "amountType": "Amount type", "amount": "Amount", "deleteDiscount": "Delete discount", "noDiscountsYet": "No discounts yet", "noDiscountsYetDescription": "Create a discount to get started", "editDiscount": "Edit discount"}, "compAndVoidPage": {"title": "Comp and Void Reasons", "description": "Set up and track reasons for comped or voided items to maintain control and transparency over order adjustments. ", "createCompAndVoidReason": "Create a Comp and Void Reason", "reason": "Reason"}, "kitchenDisplaysPage": {"title": "Your orders in one place", "description": "View all your orders within a single device, no matter where they’re placed.", "title2": "All about the details", "description2": "Our reliable tickets have all the information needed by the kitchen, including timers."}, "prepStations": {"createPrepStation": "Create Prep Station", "prepStationName": "Prep Station Name", "tags": "Categories", "actions": "Actions", "editPrepStation": "Edit Prep Station", "removePrepStation": "Are you sure you want to delete ", "noPrepStationsYet": "No Prep Stations Yet", "noPrepStationsYetDescription": "Create a prep station to get started.", "noAvailableCategories": "No categories available", "allCategoriesAssigned": "All categories are already assigned to other prep stations. Each category can only be assigned to one prep station."}, "globalSearch": {"devices": "devices", "members": "team members", "groups": "category library", "catalogs": "menus", "sellPoints": "sell points", "permissions": "permissions", "prepStations": "prep stations"}, "measureUnits": {"types": {"discrete": "", "mass": "Mass", "volume": "Volume", "length": "Length", "area": "Area", "time": "Time"}}, "itemLibrary": {"variations": "Price Variations", "identifications": "Product Codes", "categories": "Categories"}, "partnerIntegrations": {"title": "Partner Integrations", "description": "Integrate Selio with ERPs, accounting systems, and delivery platforms. Everything works seamlessly — products, orders, and payments in real time. No hassle. ", "settings": "Settings", "viewMyIntegrations": "View my integrations", "accounting": "Accounting", "onlineOrdering": "Online Ordering", "hotelPMS": "Hotel PMS", "delivery3rdParties": "Delivery 3rd Parties", "paymentProviders": "Payment Providers", "free": "Now Free", "getStarted": "Get Started", "about": "About", "features": "Features", "information": "Information", "support": "Support", "requirements": "Requirements", "supportedCountries": "Supported Countries", "developerWebsite": "Developer Website", "supportWebsite": "Support Website", "supportEmail": "Support Email", "requirementsDescription": "The seller must have an active Selio for Restaurants account and access the integration.", "winmentor": {"description": "Manage cash flows, pricing, and product catalogs — fully synchronized with Selio.", "about": "WinMentor Enterprise is a powerful ERP system used by thousands of businesses to manageaccounting, pricing, and inventory", "about2": "Selio integrates with WinMentor Enterprise so you can seamlessly sync your products, prices, and daily reports — all in one place. Keep your back-office data aligned with your front-of-house operations and streamline accounting without duplicate entries or exports.", "feature1": "Connect in Real Time", "feature1Description": "Selio connects smartly with WinMentor Enterprise for syncing accounting items with operational data in the restaurant.", "feature2": "Smart Mapping with Selio", "feature2Description": "Using shared item codes across both systems, products from WinMentor can be quickly linked to those listed in Selio for Restaurants. The integration also enables direct mapping between Selio Prep Stations and WinMentor Enterprise warehouses, ensuring full traceability across sales, stock, and accounting. Everything stays perfectly in sync — no duplicate entries."}, "sagaSoftware": {"description": "SAGA Software is a widely used accounting application in Romania, offering essential tools for managing invoices, cash registers, and inventory.", "about": "SAGA Software is a powerful ERP system used by thousands of businesses to manage accounting, pricing, and inventory", "about2": "Selio integrates with SAGA to automatically import products, prices, and item catalogs, ensuring full alignment between your accounting system and restaurant operations.", "feature1": "Data Import", "feature1Description": "Selio retrieves products and prices from SAGA, eliminating manual entry and reducing errors. The sync runs — no manual exports or imports needed.", "feature2": "Smart Mapping with Selio", "feature2Description": "Items and categories are directly mapped between SAGA and Selio for Restaurants, ensuring full traceability across sales, inventory, and tax reports. All your data stays consistent and fully synced — no duplicates."}, "nextup": {"description": "Manage products, and inventory directly from NextUp. Everything synced in Selio!", "about": "NextUp ERP is a modern and flexible ERP platform, widely used in Romania for managing accounting, inventory, and back-office operations.", "about2": "The integration with Selio allows you to sync your restaurant’s products, prices, and operational data, eliminating duplicate data entry and reducing human errors. All within a connected ecosystem, perfectly aligned between front and back office.", "feature1": "Data Import", "feature1Description": "Selio connects intelligently with NextUp ERP to synchronize accounting items with those used in the restaurant — including products, categories, prices, and master data.", "feature2": "Smart Mapping with Selio", "feature2Description": "Products from NextUp can be linked to those in Selio for Restaurants using shared codes and categories. The integration enables direct mapping between NextUp warehouses and Selio production stations, ensuring full traceability from stock to sales and accounting."}, "fivestar": {"description": "Connect hotel bookings with restaurant sales. Everything synced in Selio.", "about": "5Star Desk is a complete, cloud-based hotel management system used by hotels, guesthouses, and lodging units across Romania.", "about2": "The integration with Selio enables a direct connection between room bookings and restaurant or bar orders. You can transfer guest charges directly to their room, without handwritten notes or manual input. Synchronization is automatic and real-time, and reports include both Selio transactions and 5Star Desk balances.", "feature1": "Room Charges in Real Time", "feature1Description": "<PERSON><PERSON> connects with 5Star Desk to automatically send restaurant or bar orders to the guest’s room. Everything happens instantly, with no manual intervention.", "feature2": "Full Guest Sync", "feature2Description": "Guest data (check-in, check-out, room number) is synchronized with <PERSON><PERSON>, ensuring a smooth, error-free experience. Staff can add charges directly from the app, which instantly appear in the guest’s room account.", "feature3": "No More Paper Slips", "feature3Description": "Say goodbye to paper slips and assignment errors. All orders are recorded digitally and sent directly to the guest’s account in 5Star Desk."}, "orderNow": {"description": "Quick orders via QR, no online payment, sent straight to the POS.", "about": "OrderNow is the Selio module that allows guests to scan a QR code at their table, browse the live digital menu, and instantly send their orders straight to the restaurant’s POS. No online payment — everything stays personal, with the waiter finalizing the bill at the table. Fast, elegant, and perfectly integrated into the Selio ecosystem.", "about2": "The integration with Selio allows you to sync your restaurant’s online orders with those in the restaurant, eliminating duplicate data entry and reducing human errors. All within a connected ecosystem, perfectly aligned between front and back office.", "feature1": "Online Orders", "feature1Description": "✅ Unique QR scan per table", "feature1Description2": "✅ Live digital menu (synced with <PERSON><PERSON>)", "feature1Description3": "✅ Place orders directly from the guest’s phone", "feature2": "Staff & Operations", "feature2Description": "✅ Orders reach the Selio POS instantly, assigned to the correct table.", "feature2Description2": "✅ Waiters handle order follow-up, edits, and payments with zero workflow changes.", "feature2Description3": "✅ Fully integrated with existing printers and KDS stations, no new processes required.", "feature2Description4": "✅ Reduce errors and speed up service.", "feature3": "Management & AI", "feature3Description": "OrderNow delivers valuable data directly into Selio Manager, enabling precise order monitoring, performance analysis by table or location, and seamless integration into Selio AI reports and predictions. Managers gain a complete view of operations, can quickly spot low tips, suspicious voids, or upsell opportunities — turning every order into actionable insights that optimize the business.", "requirementsDescription": "The seller must have an active Selio for Restaurants account and access the integration."}}, "myIntegrations": {"createIntegration": "Create Integration with", "editIntegration": "Edit Integration", "itemsAndModifiers": "Items and Modifiers Codes", "itemsCode": "Items Code", "modifiersCode": "Modifiers Code", "giftCards": "Gift Cards", "tips": "Tips", "sellPoints": "Locations", "addPrepStation": "Add Prep Station", "prepStations": "Prep Stations", "symbol": "Symbol", "symbolWm": "Symbol WM", "prepStation": "Prep Station", "sellPoint": "Location", "code": "Code WM", "physical": "Physical", "digital": "Digital", "monetaryBookSymbol": "Monetary Book Symbol", "monetaryNumberPrefix": "Monetary Number Prefix", "usesWMEnterprise": "Uses WinMentor Enterprise", "paymentTypeCollectors": "Payment Type Collected", "paymentTypes": "Payment Types", "addPaymentCollector": "Add Payment Collector", "selectCollectorType": "Select collector type", "monetaryBookSymbolTooltip": "Go to Mentor → Document Booklets, then locate the Cash Register booklet and fill in the corresponding series.", "monetaryNumberPrefixTooltip": "Unique identifier for the selected location. Example: 1, 2, or 3.", "paymentTypeCollectorsTooltip": "Check, Cash, Card, Gift Voucher – payment types from WinMentor.", "paymentTypeCollectorsNameTooltip": "Identifies the specific cash register (CASSA) name used in WinMentor for transaction tracking.", "itemsCodeTooltip": "Enter the coding field used: SKU, GTIN, or EAN.", "viewIntegration": "View Integration", "saga": {"createIntegration": "Create SAGA Integration", "editIntegration": "Edit SAGA Integration", "itemsAndModifiers": "Items and Modifiers Codes", "itemsCodeField": "Items Code Field", "modifiersCodeField": "Modifiers Code Field", "tips": "Tips", "tipsCode": "Tips Code", "tipsSymbol": "Tips Symbol", "tipsCodeTooltip": "Enter the code field used for tips in SAGA", "tipsSymbolTooltip": "Enter the symbol used for tips in SAGA", "giftCards": "Gift Cards", "physicalGiftCards": "Physical Gift Cards", "digitalGiftCards": "Digital Gift Cards", "physicalGiftCardCode": "Physical Gift Card Code", "physicalGiftCardSymbol": "Physical Gift Card Symbol", "digitalGiftCardCode": "Digital Gift Card Code", "digitalGiftCardSymbol": "Digital Gift Card Symbol", "locations": "Locations", "locationConfiguration": "Location Configuration", "locationNumberPrefix": "Location Number Prefix", "locationNumberPrefixTooltip": "Unique identifier for the selected location. Example: LOC1, LOC2, or LOC3.", "groupAllCompsInSameDocument": "Group all Comps in same document", "prepStations": "Prep Stations", "addPrepStation": "Add Prep Station", "prepStationName": "Prep Station Name", "symbol": "Symbol", "noLocationsAvailable": "No locations available"}}, "workingArea": {"title": "Working Area Assignment", "description": "Assign waiters to specific tables with QR codes. Customers scan, order, and their orders are routed to the assigned staff.", "workingArea": "Working Area", "member": "Member", "noWorkingAreasYet": "No working areas assigned yet", "noWorkingAreasYetDescription": "Create a working area to get started.", "createWorkingArea": "Create Working Area", "editWorkingArea": "Edit Working Area", "selectTablesFromFloorPlans": "Select Tables from Floor Plans", "selectTablesFromFloorPlansDescription": "Choose tables from different floor plans that this team member will be responsible for.", "selectTablesFromFloorPlansNote": "Note: Some tables are already assigned to other team members and cannot be selected.", "selected": "selected", "selectAll": "Select All", "setupWorkingArea": "Setup Working Area"}, "connectionMonitor": {"titleNoInternet": "No Internet Connection", "descriptionNoInternet": "SELIO Manager needs an internet connection to save your work and keep everything synchronized. Your current work is safe, but you won't be able to make changes until the connection is restored.", "titleFirebaseDisconnected": "Server Connection Issue", "descriptionFirebaseDisconnected": "You have an internet connection, but we're having trouble connecting to our data servers. Your work is safe and we're automatically trying to reconnect. Please wait a moment while we restore the connection.", "checking": "Checking...", "checkAgain": "Check Again", "whatDoesThisMean": "What does this mean?", "whyDoINeedAnInternetConnection": "Why do I need an internet connection?", "yourInternetConnectionIsWorkingFine": "Your internet connection is working fine", "thereIsATemporaryIssueConnectingToOurDataServers": "There's a temporary issue connecting to our data servers", "weAreAutomaticallyTryingToReconnectInTheBackground": "We're automatically trying to reconnect in the background", "yourWorkIsSafeAndWillSyncOnceReconnected": "Your work is safe and will sync once reconnected", "yourChangesNeedToBeSavedToOurSecureServers": "Your changes need to be saved to our secure servers", "dataMustBeSynchronizedAcrossAllYourDevices": "Data must be synchronized across all your devices", "realTimeUpdatesEnsureEveryoneSeesTheLatestInformation": "Real-time updates ensure everyone sees the latest information", "yourWorkIsAutomaticallyBackedUpAsYouGo": "Your work is automatically backed up as you go"}}