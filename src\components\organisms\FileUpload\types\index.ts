import { UploadedFile, PrivateFileContext, ImageEditorConfig } from '~/types/fileUpload';

/**
 * Validation error for file upload
 */
export interface ValidationError {
    id: string;
    message: string;
    severity: 'error' | 'warning';
    fileName?: string;
    file?: File;
}

/**
 * Image configuration for file upload
 */
export interface ImageConfig {
    targetSizes?: Array<{
        width: number;
        height: number;
        key?: string;
        name?: string;
    }>;
    quality?: number;
    autoGenerateThumbnail: boolean;
    editorConfig?: ImageEditorConfig;
}

/**
 * Validation configuration
 */
export interface ValidationConfig {
    minFileCount?: number;
    minFileSize?: number;
    allowInvalidFiles?: boolean;
    customValidation?: (file: File) => string | null;
}

/**
 * UI configuration
 */
export interface UIConfig {
    variant?: 'default' | 'compact';
    showProgress?: boolean;
    disabled?: boolean;
    readOnly?: boolean;
    placeholder?: string;
    dragAndDrop?: boolean;
    showValidationErrors?: boolean;
    validationDisplay?: 'inline' | 'toast' | 'both';
}

/**
 * Callback configuration
 */
export interface CallbackConfig {
    onValidationError?: (errors: ValidationError[]) => void;
    onUploadSuccess?: (files: UploadedFile[]) => void;
    onUploadError?: (error: Error) => void;
    onFileUploaded?: (file: UploadedFile) => void;
    onFileMoved?: (file: UploadedFile) => void;
    onFileDeleted?: (file: UploadedFile) => void;
    onFilesUpdated?: (files: UploadedFile[]) => void;
    onImageEdit?: (originalFile: File, editedFiles: File[]) => void;
    onImageEditCancel?: (file: File) => void;
    onImageEditStart?: (file: File) => void;
}

/**
 * Main file upload configuration
 */
export interface FileUploadConfig {
    // File constraints
    fileType: 'images' | 'videos' | 'public' | 'private';
    multiple: boolean;
    maxFiles: number;
    maxSize: number;
    acceptedTypes: string[];

    // Image processing
    imageConfig?: ImageConfig;

    // Validation
    validation?: ValidationConfig;

    // UI behavior
    ui?: UIConfig;

    // Callbacks
    callbacks?: CallbackConfig;

    // Context for private files
    privateContext?: PrivateFileContext;
}

/**
 * Props for FileUploadComponent
 */
export interface FileUploadComponentProps {
    value: UploadedFile[];
    onChange: (files: UploadedFile[]) => void;
    config: FileUploadConfig;
    label?: string;
    helperText?: string;
    infoText?: string;
    error?: boolean;
    errorText?: string;
}

/**
 * Props for RaFileUploadComponent (React-Admin wrapper)
 */
export interface RaFileUploadComponentProps {
    source: string;
    config: FileUploadConfig;
    label?: string;
    helperText?: string;
    infoText?: string;
    validate?: any;
    required?: boolean;
    [key: string]: any; // Allow additional react-admin props
}

/**
 * Upload state for hooks
 */
export interface UploadState {
    uploading: boolean;
    progress: number;
    queue: File[];
    errors: Error[];
}

/**
 * File list state for hooks
 */
export interface FileListState {
    files: UploadedFile[];
    draggedIndex: number | null;
    hoveredIndex: number | null;
}

/**
 * Image editor state for hooks
 */
export interface ImageEditorState {
    isOpen: boolean;
    currentFile: File | null;
    config: ImageEditorConfig | null;
    batchInfo?: {
        current: number;
        total: number;
    };
}

/**
 * Validation state for hooks
 */
export interface ValidationState {
    errors: ValidationError[];
    warnings: ValidationError[];
    isValid: boolean;
}
