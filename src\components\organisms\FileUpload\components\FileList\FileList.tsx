import React, { useCallback } from 'react';
import {
  closestCenter,
  DndContext,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
} from '@dnd-kit/sortable';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { PrivateFileContext, UploadedFile } from '~/types/fileUpload';
import { FileItem } from './FileItem';

/**
 * File list component for displaying uploaded files
 */
interface FileListProps {
  files: UploadedFile[];
  onRemoveFile: (index: number) => void;
  onReorderFiles?: (fromIndex: number, toIndex: number) => void;
  onFilePreview?: (file: UploadedFile) => void;
  disabled?: boolean;
  readOnly?: boolean;
  multiple?: boolean;
  variant?: 'default' | 'compact';
  privateFileContext?: PrivateFileContext;
  showSummary?: boolean;
}

export const FileList: React.FC<FileListProps> = ({
  files,
  onRemoveFile,
  onReorderFiles,
  onFilePreview,
  disabled = false,
  readOnly = false,
  multiple = false,
  variant = 'default',
  privateFileContext,
  showSummary = true,
}) => {
  const { t } = useTranslation();

  // Set up sensors for @dnd-kit
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag end for reordering
  const handleDragEnd = useCallback(
    (event: any) => {
      const { active, over } = event;

      if (active.id !== over?.id && onReorderFiles) {
        // Extract the index from the ID (format: "filename-index")
        const getIndexFromId = (id: string) => {
          const match = id.match(/-(\d+)$/);
          return match ? parseInt(match[1], 10) : -1;
        };

        const oldIndex = getIndexFromId(active.id);
        const newIndex = getIndexFromId(over.id);

        if (oldIndex !== -1 && newIndex !== -1) {
          onReorderFiles(oldIndex, newIndex);
        }
      }
    },
    [onReorderFiles]
  );

  if (files.length === 0) {
    return null;
  }

  return (
    <Box sx={{ mt: disabled || readOnly ? 0 : 2 }}>
      {/* Show summary only if enabled and not in disabled/readOnly mode */}
      {showSummary && !disabled && !readOnly && (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            {multiple
              ? t('fileUpload.filesUploaded', '{{count}} file(s) uploaded:', {
                  count: files.length,
                })
              : t('fileUpload.fileUploaded', 'Uploaded file:')}
          </Typography>
        </Box>
      )}

      {multiple && onReorderFiles ? (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={disabled || readOnly ? () => {} : handleDragEnd}
        >
          <SortableContext
            items={files.map((file, index) => `${file.rn || file.fn}-${index}`)}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
              }}
            >
              {files.map((file, index) => (
                <FileItem
                  key={`${file.rn || file.fn}-${index}`}
                  file={file}
                  index={index}
                  onRemove={onRemoveFile}
                  onPreview={onFilePreview}
                  disabled={disabled}
                  readOnly={readOnly}
                  variant={variant}
                  sortable={true}
                  sortableId={`${file.rn || file.fn}-${index}`}
                />
              ))}
            </Box>
          </SortableContext>
        </DndContext>
      ) : (
        <Box
          sx={{
            display: 'flex',
            flexDirection: variant === 'compact' ? 'row' : 'column',
            gap: 1,
            flexWrap: variant === 'compact' ? 'wrap' : 'nowrap',
          }}
        >
          {files.map((file, index) => (
            <FileItem
              key={`${file.rn || file.fn}-${index}`}
              file={file}
              index={index}
              onRemove={onRemoveFile}
              onPreview={onFilePreview}
              disabled={disabled}
              readOnly={readOnly}
              variant={variant}
              sortable={false}
            />
          ))}
        </Box>
      )}
    </Box>
  );
};
