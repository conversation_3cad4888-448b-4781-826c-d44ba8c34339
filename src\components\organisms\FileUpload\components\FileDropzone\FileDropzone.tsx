import React, { useCallback, useRef, useState } from 'react';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { Box, Paper, Typography, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

/**
 * File dropzone component for drag and drop functionality
 */
interface FileDropzoneProps {
  onFilesSelected: (files: File[]) => void;
  disabled?: boolean;
  multiple?: boolean;
  acceptedTypes?: string[];
  maxFiles?: number;
  placeholder?: string;
  error?: boolean;
  uploading?: boolean;
  children?: React.ReactNode;
  variant?: 'default' | 'compact';
}

export const FileDropzone: React.FC<FileDropzoneProps> = ({
  onFilesSelected,
  disabled = false,
  multiple = false,
  acceptedTypes = [],
  maxFiles,
  placeholder,
  error = false,
  uploading = false,
  children,
  variant = 'default',
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const [dragOver, setDragOver] = useState(false);
  const [inputKey, setInputKey] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Generate unique ID for input
  const uniqueId = useRef(
    `file-dropzone-${Math.random().toString(36).substring(2, 11)}`
  );

  // Handle drag events
  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      if (!disabled && !uploading) {
        setDragOver(true);
      }
    },
    [disabled, uploading]
  );

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setDragOver(false);

      if (disabled || uploading) return;

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        onFilesSelected(files);
      }
    },
    [disabled, uploading, onFilesSelected]
  );

  // Handle file input change
  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        onFilesSelected(Array.from(files));
      }
      // Reset input to allow selecting the same file again
      setInputKey(prev => prev + 1);
    },
    [onFilesSelected]
  );

  // Handle click to open file dialog
  const handleClick = useCallback(() => {
    if (!disabled && !uploading && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [disabled, uploading]);

  // Get placeholder text
  const getPlaceholderText = useCallback(() => {
    if (placeholder) return placeholder;

    if (uploading) return t('fileUpload.uploading', 'Uploading...');

    if (multiple) {
      return maxFiles
        ? t(
            'fileUpload.dropMultipleWithLimit',
            `Drop files here or click to browse (max ${maxFiles})`
          )
        : t('fileUpload.dropMultiple', 'Drop files here or click to browse');
    }

    return t('fileUpload.dropSingle', 'Drop a file here or click to browse');
  }, [placeholder, uploading, multiple, maxFiles, t]);

  // Get accepted types text
  const getAcceptedTypesText = useCallback(() => {
    if (acceptedTypes.length === 0) return '';

    const types = acceptedTypes.map(type => {
      if (type.startsWith('image/')) return 'Images';
      if (type.startsWith('video/')) return 'Videos';
      if (type.includes('pdf')) return 'PDF';
      if (type.includes('word')) return 'Word';
      if (type.includes('excel') || type.includes('spreadsheet'))
        return 'Excel';
      return type.split('/')[1]?.toUpperCase() || type;
    });

    const uniqueTypes = [...new Set(types)];
    return uniqueTypes.join(', ');
  }, [acceptedTypes]);

  if (variant === 'compact') {
    return (
      <Box sx={{ display: 'inline-flex', alignItems: 'center' }}>
        <input
          key={inputKey}
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={acceptedTypes.join(',')}
          onChange={handleFileInputChange}
          disabled={disabled || uploading}
          style={{ display: 'none' }}
          id={uniqueId.current}
        />
        <label
          htmlFor={uniqueId.current}
          style={{
            cursor: disabled || uploading ? 'not-allowed' : 'pointer',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          {children}
        </label>
      </Box>
    );
  }

  return (
    <Paper
      variant="outlined"
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={handleClick}
      sx={{
        p: 3,
        textAlign: 'center',
        cursor: disabled || uploading ? 'not-allowed' : 'pointer',
        border: error
          ? `2px dashed ${theme.palette.error.main}`
          : dragOver
            ? `2px dashed ${theme.palette.primary.main}`
            : `2px dashed ${theme.palette.divider}`,
        backgroundColor: error
          ? theme.palette.error.light
          : dragOver
            ? theme.palette.primary.light
            : 'transparent',
        transition: 'border-color 0.2s, background-color 0.2s',
        opacity: disabled ? 0.6 : 1,
        '&:hover':
          disabled || uploading
            ? {}
            : {
                borderColor: error
                  ? theme.palette.error.main
                  : theme.palette.primary.main,
                backgroundColor: error
                  ? theme.palette.error.light
                  : theme.palette.primary.light,
              },
      }}
    >
      <input
        key={inputKey}
        ref={fileInputRef}
        type="file"
        multiple={multiple}
        accept={acceptedTypes.join(',')}
        onChange={handleFileInputChange}
        disabled={disabled || uploading}
        style={{ display: 'none' }}
        id={uniqueId.current}
      />

      <CloudUploadIcon
        sx={{
          fontSize: 48,
          color: error
            ? theme.palette.error.main
            : dragOver
              ? theme.palette.primary.main
              : theme.palette.text.secondary,
          mb: 2,
        }}
      />

      <Typography
        variant="body1"
        sx={{
          mb: 1,
          color: error ? theme.palette.error.main : theme.palette.text.primary,
        }}
      >
        {getPlaceholderText()}
      </Typography>

      {getAcceptedTypesText() && (
        <Typography
          variant="caption"
          sx={{
            color: theme.palette.text.secondary,
            display: 'block',
          }}
        >
          {t('fileUpload.acceptedTypes', 'Accepted types: {{types}}', {
            types: getAcceptedTypesText(),
          })}
        </Typography>
      )}

      {children}
    </Paper>
  );
};
